<template>
  <div class="template-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">工作流模板</h1>
        <p class="page-subtitle">选择合适的模板快速创建工作流</p>
      </div>
    </div>

    <!-- 模板网格 -->
    <div class="templates-grid">
      <div
        v-for="template in filteredTemplates"
        :key="template.id"
        class="template-card"
        @click="previewTemplate(template)"
      >
        <div class="card-header">
          <div class="template-icon">
            <el-icon :size="32" :color="template.iconColor">
              <component :is="template.icon" />
            </el-icon>
          </div>
          <div class="card-actions">
            <el-tooltip content="收藏模板" placement="top">
              <el-button
                type="text"
                :icon="template.isFavorite ? 'StarFilled' : 'Star'"
                :class="{ favorited: template.isFavorite }"
                @click.stop="toggleFavorite(template)"
              />
            </el-tooltip>
          </div>
        </div>

        <div class="card-content">
          <h3 class="template-title">{{ template.name }}</h3>
          <p class="template-description">{{ template.description }}</p>

          <div class="template-tags">
            <el-tag
              v-for="tag in template.tags"
              :key="tag"
              size="small"
              type="info"
              class="template-tag"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <div class="card-footer">
          <div class="template-stats">
            <span class="stat-item">
              <el-icon><User /></el-icon>
              {{ template.useCount }}次使用
            </span>
            <span class="stat-item">
              <el-icon><Clock /></el-icon>
              {{ formatDate(template.updateTime) }}
            </span>
          </div>

          <div class="card-buttons">
            <el-button
              type="primary"
              size="small"
              @click.stop="useTemplate(template)"
            >
              使用模板
            </el-button>
            <el-button
              type="default"
              size="small"
              @click.stop="previewTemplate(template)"
            >
              预览
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredTemplates.length === 0" class="empty-state">
      <el-empty description="暂无符合条件的模板" image-size="120" />
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="模板预览"
      width="800px"
      class="preview-dialog"
    >
      <div v-if="selectedTemplate" class="preview-content">
        <div class="preview-header">
          <div class="preview-icon">
            <el-icon :size="48" :color="selectedTemplate.iconColor">
              <component :is="selectedTemplate.icon" />
            </el-icon>
          </div>
          <div class="preview-info">
            <h2>{{ selectedTemplate.name }}</h2>
            <p class="preview-description">
              {{ selectedTemplate.description }}
            </p>
            <div class="preview-tags">
              <el-tag
                v-for="tag in selectedTemplate.tags"
                :key="tag"
                size="small"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="preview-details">
          <h4>模板详情</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>创建者：</label>
              <span>{{ selectedTemplate.creator }}</span>
            </div>
            <div class="detail-item">
              <label>使用次数：</label>
              <span>{{ selectedTemplate.useCount }}次</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDate(selectedTemplate.updateTime) }}</span>
            </div>
            <div class="detail-item">
              <label>节点数量：</label>
              <span>{{ selectedTemplate.nodeCount }}个</span>
            </div>
          </div>
        </div>

        <div class="preview-workflow">
          <h4>工作流预览</h4>
          <div class="workflow-diagram">
            <p class="diagram-placeholder">工作流图表预览区域</p>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="useSelectedTemplate">
            使用此模板
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import {
  Search,
  Star,
  StarFilled,
  User,
  Clock,
  DataAnalysis,
  Setting,
  Monitor,
  DocumentCopy,
  Operation,
  Platform,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";

// 响应式数据
const searchKeyword = ref("");
const selectedCategory = ref("");
const sortBy = ref("newest");
const previewDialogVisible = ref(false);
const selectedTemplate = ref(null);

// 分类选项
const categories = ref([
  { label: "数据处理", value: "data" },
  { label: "机器学习", value: "ml" },
  { label: "系统监控", value: "monitor" },
  { label: "业务流程", value: "business" },
  { label: "数据分析", value: "analysis" },
]);

// 模拟模板数据
const templates = ref([
  {
    id: 1,
    name: "数据预处理流水线",
    description: "用于清洗、转换和验证原始数据的标准化流程模板",
    category: "data",
    tags: ["数据清洗", "数据转换", "质量检查"],
    icon: "DataAnalysis",
    iconColor: "#409EFF",
    creator: "张三",
    useCount: 156,
    updateTime: "2024-05-28",
    nodeCount: 8,
    isFavorite: false,
  },
  {
    id: 2,
    name: "机器学习训练流程",
    description: "包含数据准备、模型训练、验证和部署的完整ML工作流",
    category: "ml",
    tags: ["机器学习", "模型训练", "自动化"],
    icon: "Setting",
    iconColor: "#67C23A",
    creator: "李四",
    useCount: 89,
    updateTime: "2024-05-27",
    nodeCount: 12,
    isFavorite: true,
  },
  {
    id: 3,
    name: "系统健康监控",
    description: "实时监控系统性能指标，自动报警和故障恢复",
    category: "monitor",
    tags: ["监控", "告警", "自动化运维"],
    icon: "Monitor",
    iconColor: "#E6A23C",
    creator: "王五",
    useCount: 234,
    updateTime: "2024-05-29",
    nodeCount: 6,
    isFavorite: false,
  },
  {
    id: 4,
    name: "业务流程审批",
    description: "标准化的业务审批流程，支持多级审批和条件分支",
    category: "business",
    tags: ["审批", "流程管理", "业务自动化"],
    icon: "DocumentCopy",
    iconColor: "#F56C6C",
    creator: "赵六",
    useCount: 67,
    updateTime: "2024-05-26",
    nodeCount: 10,
    isFavorite: false,
  },
  {
    id: 5,
    name: "数据分析报告生成",
    description: "自动化数据分析和报告生成，支持多种图表和导出格式",
    category: "analysis",
    tags: ["数据分析", "报告生成", "可视化"],
    icon: "Operation",
    iconColor: "#909399",
    creator: "钱七",
    useCount: 123,
    updateTime: "2024-05-30",
    nodeCount: 15,
    isFavorite: true,
  },
  {
    id: 6,
    name: "批量文件处理",
    description: "高效处理大量文件的转换、压缩和分发任务",
    category: "data",
    tags: ["文件处理", "批量操作", "自动化"],
    icon: "Platform",
    iconColor: "#1A59B0",
    creator: "孙八",
    useCount: 98,
    updateTime: "2024-05-25",
    nodeCount: 7,
    isFavorite: false,
  },
]);

// 计算属性 - 过滤后的模板
const filteredTemplates = computed(() => {
  let result = templates.value;

  // 关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      (template) =>
        template.name.toLowerCase().includes(keyword) ||
        template.description.toLowerCase().includes(keyword) ||
        template.tags.some((tag) => tag.toLowerCase().includes(keyword))
    );
  }

  // 分类过滤
  if (selectedCategory.value) {
    result = result.filter(
      (template) => template.category === selectedCategory.value
    );
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case "newest":
        return new Date(b.updateTime) - new Date(a.updateTime);
      case "popular":
        return b.useCount - a.useCount;
      case "name":
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  return result;
});

// 方法
const formatDate = (dateString) => {
  return dayjs(dateString).format("YYYY-MM-DD");
};

const toggleFavorite = (template) => {
  template.isFavorite = !template.isFavorite;
  ElMessage.success(template.isFavorite ? "已添加到收藏" : "已取消收藏");
};

const previewTemplate = (template) => {
  selectedTemplate.value = template;
  previewDialogVisible.value = true;
};

const useTemplate = (template) => {
  ElMessage.success(`正在使用模板：${template.name}`);
  // 这里可以添加跳转到编辑器的逻辑
};

const useSelectedTemplate = () => {
  if (selectedTemplate.value) {
    useTemplate(selectedTemplate.value);
    previewDialogVisible.value = false;
  }
};

// 生命周期
onMounted(() => {
  console.log("工作流模板页面已加载");
});
</script>

<style lang="scss" scoped>
.template-list-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #1a59b0 0%, #207cb8 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
  }

  .page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  .page-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.template-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #1a59b0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .template-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      border-radius: 12px;
      background: rgba(26, 89, 176, 0.1);
    }

    .card-actions {
      .favorited {
        color: #e6a23c;
      }
    }
  }

  .card-content {
    margin-bottom: 16px;

    .template-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }

    .template-description {
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
      margin: 0 0 12px 0;
    }

    .template-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .template-tag {
        font-size: 12px;
      }
    }
  }

  .card-footer {
    .template-stats {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #909399;

        .el-icon {
          font-size: 14px;
        }
      }
    }

    .card-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
}

.preview-dialog {
  .preview-content {
    .preview-header {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      .preview-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72px;
        height: 72px;
        border-radius: 16px;
        background: rgba(26, 89, 176, 0.1);
        flex-shrink: 0;
      }

      .preview-info {
        flex: 1;

        h2 {
          margin: 0 0 8px 0;
          color: #303133;
        }

        .preview-description {
          color: #606266;
          margin: 0 0 12px 0;
          line-height: 1.5;
        }

        .preview-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
      }
    }

    .preview-details {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
      }

      .detail-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .detail-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          span {
            color: #303133;
          }
        }
      }
    }

    .preview-workflow {
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
      }

      .workflow-diagram {
        height: 200px;
        background: #f5f7fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        .diagram-placeholder {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .template-list-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px 20px;

    .page-title {
      font-size: 24px;
    }
  }

  .templates-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filter-section {
    flex-direction: column;
    gap: 12px !important;

    .filter-group {
      justify-content: space-between;

      .category-select,
      .sort-select {
        flex: 1;
        margin-left: 12px;
      }
    }
  }
}
</style>
