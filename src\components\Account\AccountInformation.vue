<template>
  <el-tabs v-model="activeName" @tab-change="handleChange">
    <el-tab-pane
      v-for="item in tabs"
      :label="item.label"
      :name="item.name"
      :key="item.name"
    >
      <el-descriptions
        v-if="activeName === 'user'"
        :column="1"
        label-width="150px"
        border
      >
        <el-descriptions-item
          v-for="(item, index) in userItems"
          :label="item.label"
          :key="index"
        >
          {{ userInfo[item.value] }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-if="activeName == 'hpc'">
        <el-table :data="hpcData" style="width: 100%" show-overflow-tooltip>
          <el-table-column
            v-for="item in tableItems"
            :key="item.value"
            :prop="item.value"
            :label="item.label"
            :width="item.width || 'auto'"
            :formatter="(row) => formatData(row, item)"
          ></el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-button
                v-if="row.extension1 === 'START'"
                @click="handleShow(row)"
                type="primary"
                size="small"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { to, user_info, hpc_auth_list } from "@/api/index";

// tabs
const activeName = ref("user");
const tabs = ref([
  { label: "用户信息", name: "user" },
  { label: "授权HPC", name: "hpc" },
]);

// 用户信息
const userItems = ref([
  { label: "用户ID", value: "userId" },
  { label: "组ID", value: "groupId" },
  { label: "邮箱", value: "email" },
  { label: "用户名", value: "userName" },
  { label: "电话", value: "mobile" },
  { label: "状态", value: "status" },
  { label: "单位或机构", value: "platformName" },
  { label: "部门或课题组", value: "platformUser" },
  { label: "文件路径", value: "filesRoot" },
  { label: "最近登录时间", value: "lastAccessTime" },
]);
const userInfo = ref({});
const fetchUserInfo = async () => {
  const [err, res] = await to(() => user_info());
  if (err) {
    console.error("获取用户信息失败:", err);
    return;
  }
  console.log(res.data);
  userInfo.value = res.data;
};

// HPC授权内容
// 表格列配置
const tableItems = ref([
  { label: "用户ID", value: "userId", width: "140" },
  { label: "邮箱", value: "email" },
  { label: "HPC ID", value: "hpcId", width: "100" },
  { label: "超算账号", value: "hpcAccount", width: "100" },
  { label: "类型", value: "operType", width: "100" },
  { label: "状态", value: "extension1", width: "80" },
  { label: "HPC作业根路径", value: "extension2" },
]);
const hpcData = ref([]);

const fetchHpcList = async () => {
  const [err, res] = await to(() => hpc_auth_list());
  if (err) {
    console.error("获取HPC授权列表失败:", err);
    return;
  }
  console.log(res.data);
  hpcData.value = res.data;
};

const formatData = (row, item) => {
  if (item.value === "operType") {
    return row[item.value] === 0 ? "COMMON" : "USER";
  }
  return row[item.value] || "-";
};

// 查看任务详情
const handleShow = (row) => {
  window.open(
    `http://www.aicnic.cn/jobs-solver/#/weblogin?applicationname=solver&email=${row.email}&hpcid=${row.hpcId}&account=${row.hpcAccount}`,
    "_blank"
  );
};

const handleChange = (val) => {
  if (val === "hpc") {
    fetchHpcList();
  } else if (val === "user") {
    fetchUserInfo();
  }
};

onMounted(() => {
  fetchUserInfo();
});
</script>

<style lang="scss" scoped>
.ml-3 {
  margin-left: 12px;
}
</style>
