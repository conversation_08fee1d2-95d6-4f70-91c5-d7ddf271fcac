import dayjs from "dayjs";

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期
 * @param {string} format - 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = "YYYY-MM-DD HH:mm:ss") => {
  if (!date) return "";

  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
};

/**
 * 格式化日期时间（使用dayjs）
 * @param {Date|string|number} dateTime - 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDateTime = (dateTime) => {
  if (!dateTime) return "--";

  // 如果是时间戳（数字）
  if (typeof dateTime === "number") {
    return dayjs.unix(dateTime).format("YYYY-MM-DD HH:mm:ss");
  }

  // 如果是字符串格式的日期
  if (typeof dateTime === "string") {
    const date = dayjs(dateTime);
    if (date.isValid()) {
      return date.format("YYYY-MM-DD HH:mm:ss");
    }
  }

  return dateTime;
};

export const to = (fn) => {
  return fn()
    .then((data) => [null, data])
    .catch((err) => [err, null]);
};
