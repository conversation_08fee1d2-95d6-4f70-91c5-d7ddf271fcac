import { defineStore } from "pinia";
import { user_login, user_logout } from "@/api/index";

/**
 * 用户信息 Store
 * 支持持久化存储到 localStorage
 */
export const userStore = defineStore("user", {
  state: () => ({
    isLogin: false,
    token: "",
  }),

  actions: {
    /**
     * 用户登录
     * @param {Object} loginData - 登录数据
     */
    async login(loginData) {
      try {
        // 这里应该调用登录API
        const response = await user_login(loginData);

        localStorage.setItem("token", response.data.token); // 假设API返回的token在data.token中
        this.isLogin = true;
        return true;
      } catch (error) {
        // console.error("登录失败:", error);
        return false;
      }
    },

    /**
     * 用户登出
     */
    async logout() {
      try {
        // 这里应该调用登出API
        await user_logout();
        this.resetUserState();
        return true;
      } catch (error) {
        console.error("登出失败:", error);
        // 即使API调用失败也要清除本地状态
        this.resetUserState();
        return false;
      }
    },

    /**
     * 重置用户状态
     */
    resetUserState() {
      this.isLogin = false;
      this.token = "";
    },
  },

  // 持久化配置
  //   persist: {
  //     key: "token",
  //     storage: localStorage,
  //     // 指定需要持久化的字段
  //     paths: ["token"],
  //   },
});
