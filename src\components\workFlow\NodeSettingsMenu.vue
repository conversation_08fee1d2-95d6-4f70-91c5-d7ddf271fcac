<template>
  <teleport to="body">
    <div 
      v-if="visible" 
      class="node-settings-menu-overlay"
      @click="handleOverlayClick"
    >
      <div 
        class="node-settings-menu"
        :style="menuStyle"
        @click.stop
      >
        <div class="menu-header">
          <div class="node-info">
            <el-icon class="node-icon"><Box /></el-icon>
            <span class="node-name">{{ nodeData?.name || '未命名节点' }}</span>
          </div>
          <el-button 
            type="text" 
            size="small" 
            @click="handleClose"
            class="close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <div class="menu-content">
          <div class="menu-section">
            <div class="section-title">节点操作</div>
            <div class="menu-items">
              <div 
                class="menu-item"
                @click="handleAction('edit')"
              >
                <el-icon><Edit /></el-icon>
                <span>编辑节点</span>
              </div>
              
              <div 
                class="menu-item"
                @click="handleAction('copy')"
              >
                <el-icon><CopyDocument /></el-icon>
                <span>复制节点</span>
              </div>
              
              <div 
                class="menu-item"
                @click="handleAction('rename')"
              >
                <el-icon><EditPen /></el-icon>
                <span>重命名</span>
              </div>
            </div>
          </div>
          
          <div class="menu-section">
            <div class="section-title">节点配置</div>
            <div class="menu-items">
              <div 
                class="menu-item"
                @click="handleAction('configure')"
              >
                <el-icon><Setting /></el-icon>
                <span>参数配置</span>
              </div>
              
              <div 
                class="menu-item"
                @click="handleAction('view-logs')"
              >
                <el-icon><Document /></el-icon>
                <span>查看日志</span>
              </div>
              
              <div 
                class="menu-item"
                @click="handleAction('export')"
              >
                <el-icon><Download /></el-icon>
                <span>导出配置</span>
              </div>
            </div>
          </div>
          
          <div class="menu-section">
            <div class="section-title">危险操作</div>
            <div class="menu-items">
              <div 
                class="menu-item danger"
                @click="handleAction('delete')"
              >
                <el-icon><Delete /></el-icon>
                <span>删除节点</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { 
  Box, 
  Close, 
  Edit, 
  CopyDocument, 
  EditPen, 
  Setting, 
  Document, 
  Download, 
  Delete 
} from '@element-plus/icons-vue'

// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  nodeData: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['close', 'action'])

// 计算菜单样式
const menuStyle = computed(() => {
  const { x, y } = props.position
  const menuWidth = 240
  const menuHeight = 400
  
  // 获取视窗尺寸
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  // 计算菜单位置，确保不超出视窗
  let left = x
  let top = y
  
  if (left + menuWidth > viewportWidth) {
    left = viewportWidth - menuWidth - 10
  }
  
  if (top + menuHeight > viewportHeight) {
    top = viewportHeight - menuHeight - 10
  }
  
  // 确保菜单不会超出左边界和上边界
  left = Math.max(10, left)
  top = Math.max(10, top)
  
  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${menuWidth}px`
  }
})

// 处理菜单项点击
const handleAction = (action) => {
  emit('action', action, props.nodeData)
}

// 处理关闭
const handleClose = () => {
  emit('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  emit('close')
}

// 处理 ESC 键关闭
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.node-settings-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.node-settings-menu {
  position: absolute;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;
  overflow: hidden;
  animation: menuSlideIn 0.2s ease-out;
}

.menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  
  .node-info {
    display: flex;
    align-items: center;
    
    .node-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 16px;
    }
    
    .node-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .close-btn {
    padding: 4px;
    color: #909399;
    
    &:hover {
      color: #f56c6c;
    }
  }
}

.menu-content {
  max-height: 350px;
  overflow-y: auto;
}

.menu-section {
  padding: 8px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
  
  .section-title {
    padding: 8px 16px 4px;
    font-size: 12px;
    font-weight: 500;
    color: #909399;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.menu-items {
  .menu-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.2s;
    color: #606266;
    
    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }
    
    &.danger {
      color: #f56c6c;
      
      &:hover {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
    
    .el-icon {
      margin-right: 12px;
      font-size: 16px;
    }
    
    span {
      font-size: 14px;
    }
  }
}

// 滚动条样式
.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 动画
@keyframes menuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
