# 工作流画布增强功能实现

## 已完成的功能

### 1. 连接桩功能 ✅

- **位置**: 为所有拖拽进来的节点添加了四向连接桩（上、右、下、左）
- **可见性**: 连接桩始终可见，透明度为 0.6，鼠标悬停时变为 1.0
- **配置**: 在 `handleDrop.js` 中为创建的节点添加了端口配置
- **样式**: 蓝色圆形连接桩，磁吸效果

### 2. 节点内部按钮系统 ✅

使用 X6 的 Button Tool 系统，实现了三个内置按钮：

#### a) 运行/暂停按钮

- **位置**: 节点左上角 (x: -15, y: 15)
- **样式**: 绿色圆形按钮
- **图标**: 未运行时显示 ▶，运行时显示 ⏸
- **功能**: 切换节点运行状态，触发 `node-run-state-changed` 事件

#### b) 展开/收起按钮

- **位置**: 节点左下角 (x: -15, y: -15)
- **样式**: 蓝色圆形按钮
- **图标**: 收起时显示 +，展开时显示 −
- **功能**: 切换节点展开状态，触发 `node-expand-state-changed` 事件

#### c) 汉堡菜单按钮

- **位置**: 节点右上角 (x: 15, y: 15)
- **样式**: 橙色圆形按钮，内部三条横线
- **功能**: 显示上下文菜单，触发 `show-node-menu` 事件

### 3. 汉堡菜单功能 ✅

点击汉堡菜单按钮时显示包含以下选项的菜单：

- 📝 编辑属性 - 在属性面板中编辑节点
- 📋 复制节点 - 创建节点副本
- 🗑️ 删除节点 - 删除当前节点
- 📄 查看日志 - 查看节点运行日志
- 🔄 重置状态 - 重置节点的运行和展开状态

### 4. 事件系统 ✅

实现了完整的事件监听和处理系统：

#### WorkflowCanvas 组件触发的事件：

- `node-run-state-changed` - 节点运行状态改变
- `node-expand-state-changed` - 节点展开状态改变
- `show-node-menu` - 显示节点菜单
- `edit-node-properties` - 编辑节点属性
- `view-node-logs` - 查看节点日志

#### workspace.vue 中的事件处理：

- `handleNodeRunStateChanged()` - 处理运行状态变化
- `handleNodeExpandStateChanged()` - 处理展开状态变化
- `handleShowNodeMenu()` - 处理菜单显示
- `handleEditNodeProperties()` - 处理属性编辑
- `handleViewNodeLogs()` - 处理日志查看

### 5. 按钮可见性控制 ✅

- **显示条件**: 鼠标进入节点时显示所有按钮
- **隐藏条件**: 鼠标离开节点时隐藏所有按钮
- **状态保持**: 按钮点击后会重新创建以反映最新状态

### 6. 画布响应式设计 ✅

- **动态尺寸**: 画布自动适应父容器大小
- **窗口缩放**: 监听窗口大小变化，自动调整画布尺寸
- **防抖处理**: 100ms 防抖避免频繁调整

## 技术实现细节

### 核心文件修改：

1. **WorkflowCanvas.vue**

   - 添加了按钮创建函数
   - 实现了事件监听系统
   - 修改了端口可见性配置

2. **handleDrop.js**

   - 为拖拽创建的节点添加端口配置
   - 支持四向连接桩

3. **workspace.vue**
   - 添加了新事件的监听器
   - 实现了对应的事件处理函数

### 使用的技术：

- **X6 Button Tool**: 原生按钮工具系统
- **SVG 节点**: 支持按钮工具的 SVG 矩形节点
- **事件系统**: Vue 的自定义事件机制
- **Composition API**: Vue 3 的响应式系统

## 使用方法

### 1. 拖拽节点

从左侧工具面板拖拽节点模板到画布中，节点会自动配置连接桩和内部按钮。

### 2. 连接节点

鼠标悬停在节点上时可以看到四个连接桩，可以拖拽创建连线。

### 3. 操作按钮

鼠标进入节点后会显示三个操作按钮：

- 点击绿色按钮切换运行状态
- 点击蓝色按钮切换展开状态
- 点击橙色汉堡菜单查看更多选项

### 4. 菜单操作

点击汉堡菜单后会在控制台输出菜单信息，实际项目中可以扩展为真实的下拉菜单组件。

## 下一步可扩展功能

1. **真实菜单组件**: 使用 Element Plus 的 Dropdown 实现真实的上下文菜单
2. **节点配置面板**: 实现节点属性的可视化编辑
3. **日志查看器**: 创建日志显示对话框
4. **节点状态指示**: 添加更多状态指示器（错误、警告等）
5. **自定义按钮**: 支持根据节点类型添加自定义按钮

## 兼容性

- ✅ Vue 3 Composition API
- ✅ Element Plus UI 框架
- ✅ X6 图形引擎
- ✅ 现代浏览器（Chrome、Firefox、Safari、Edge）
