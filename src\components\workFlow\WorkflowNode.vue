<template>
  <div class="workflow-node" :class="{ 'selected': isSelected }">
    <div class="node-header">
      <div class="node-icon">
        <el-icon><Box /></el-icon>
      </div>
      <div class="node-title">{{ nodeData.name }}</div>
      <div class="node-settings" @click="handleSettingsClick">
        <el-icon><Setting /></el-icon>
      </div>
    </div>
    
    <div class="node-content" v-if="nodeData.description">
      <div class="node-description">{{ nodeData.description }}</div>
    </div>
    
    <!-- 连接点 -->
    <div class="node-ports">
      <div class="input-port" v-if="showInputPort">
        <div class="port-dot"></div>
      </div>
      <div class="output-port" v-if="showOutputPort">
        <div class="port-dot"></div>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="node-status" v-if="nodeStatus">
      <div 
        class="status-indicator" 
        :class="`status-${nodeStatus}`"
        :title="getStatusText(nodeStatus)"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Box, Setting } from '@element-plus/icons-vue'

// 定义 props
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
    default: () => ({})
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  showInputPort: {
    type: Boolean,
    default: true
  },
  showOutputPort: {
    type: Boolean,
    default: true
  },
  nodeStatus: {
    type: String,
    default: null,
    validator: (value) => {
      return ['idle', 'running', 'success', 'error', 'warning'].includes(value)
    }
  }
})

// 定义事件
const emit = defineEmits(['settings-click', 'node-click'])

// 计算属性
const nodeTitle = computed(() => {
  return props.nodeData.name || '未命名节点'
})

const nodeDescription = computed(() => {
  return props.nodeData.description || ''
})

// 方法
const handleSettingsClick = (event) => {
  event.stopPropagation()
  emit('settings-click', props.nodeData, event)
}

const handleNodeClick = (event) => {
  emit('node-click', props.nodeData, event)
}

const getStatusText = (status) => {
  const statusMap = {
    idle: '空闲',
    running: '运行中',
    success: '成功',
    error: '错误',
    warning: '警告'
  }
  return statusMap[status] || '未知状态'
}
</script>

<style lang="scss" scoped>
.workflow-node {
  position: relative;
  width: 180px;
  min-height: 60px;
  background-color: #ffffff;
  border: 2px solid #409eff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
  
  &.selected {
    border-color: #67c23a;
    box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
  }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  
  .node-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 16px;
  }
  
  .node-title {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .node-settings {
    padding: 4px;
    border-radius: 4px;
    color: #606266;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }
  }
}

.node-content {
  padding: 8px 12px;
  
  .node-description {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

.node-ports {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  
  .input-port {
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    
    .port-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #fff;
      border: 2px solid #409eff;
      pointer-events: all;
      cursor: crosshair;
      
      &:hover {
        background-color: #409eff;
      }
    }
  }
  
  .output-port {
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    
    .port-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #fff;
      border: 2px solid #409eff;
      pointer-events: all;
      cursor: crosshair;
      
      &:hover {
        background-color: #409eff;
      }
    }
  }
}

.node-status {
  position: absolute;
  top: 4px;
  right: 4px;
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.status-idle {
      background-color: #909399;
    }
    
    &.status-running {
      background-color: #409eff;
      animation: pulse 1.5s infinite;
    }
    
    &.status-success {
      background-color: #67c23a;
    }
    
    &.status-error {
      background-color: #f56c6c;
    }
    
    &.status-warning {
      background-color: #e6a23c;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
