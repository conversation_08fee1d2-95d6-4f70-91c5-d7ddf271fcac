// 处理拖拽放置事件
function handleDrop(event, graphInstance) {
  event.preventDefault();
  try {
    // 获取拖拽的数据
    const data = JSON.parse(event.dataTransfer.getData("text/plain"));
    if (!data || !graphInstance) return;

    // 计算放置位置（相对于画布）
    const { x, y } = graphInstance.clientToLocal(event.clientX, event.clientY);

    // 判断节点类型，使用不同的渲染方式
    if (data.nodeType === "class") {
      // 创建类图节点
      return createClassNode(data, x, y, graphInstance);
    }
  } catch (error) {
    console.error("处理拖拽数据失败:", error);
    return null;
  }
}

// 创建类图节点
function createClassNode(data, x, y, graphInstance) {
  const { name, dataFiles = [], methods = [], isAbstract, isInterface } = data;

  // 计算节点高度（基于内容）
  const headerHeight = 30;
  const lineHeight = 20;
  const attrHeight = dataFiles.length * lineHeight;
  const methodHeight = methods.length * lineHeight;
  const padding = 10;
  const totalHeight =
    headerHeight +
    (dataFiles.length > 0 ? attrHeight + padding : 0) +
    (methods.length > 0 ? methodHeight + padding : 0);

  // 选择节点样式
  const bgColor = isInterface ? "#e6f7ff" : isAbstract ? "#f5f5f5" : "#ffffff";
  const borderColor = "#5F95FF";

  const width = 180;
  // 使用内置的 class 节点类型
  const node = graphInstance.addNode({
    shape: "class",
    x,
    y,
    width,
    height: Math.max(120, totalHeight), // 最小高度120px
    name: name || [data.title],
    dataFiles: dataFiles,
    methods: methods,
    data: {
      ...data,
      type: "class",
    },
    // 直接在节点创建时添加端口配置
    ports: {
      groups: {
        // 输入端口组
        in: {
          position: "absolute",
          markup: [
            {
              tagName: "circle",
              selector: "circle",
              attrs: {
                r: 6,
                stroke: "#52c41a",
                fill: "#fff",
                "stroke-width": 2,
              },
            },
          ],
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: "#52c41a",
              strokeWidth: 2,
              fill: "#f6ffed",
              visibility: "visible", // 改为可见
            },
          },
        },
        // 输出端口组
        out: {
          position: "absolute",
          markup: [
            {
              tagName: "circle",
              selector: "circle",
              attrs: {
                r: 6,
                stroke: "#1890ff",
                fill: "#fff",
                "stroke-width": 2,
              },
            },
          ],
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: "#1890ff",
              strokeWidth: 2,
              fill: "#e6f7ff",
              visibility: "visible", // 改为可见
            },
          },
        },
        // 通用端口组
        default: {
          position: "absolute",
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: "#5F95FF",
              strokeWidth: 1,
              fill: "#fff",
              visibility: "hidden",
            },
          },
        },
      },
      items: [
        // 顶部连接桩
        { group: "in", id: "port-top", args: { x: "50%", y: 0 } },

        // 右侧连接桩
        { group: "out", id: "port-right", args: { x: "100%", y: "50%" } },

        // 底部连接桩
        { group: "out", id: "port-bottom", args: { x: "50%", y: "100%" } },

        // 左侧连接桩
        { group: "in", id: "port-left", args: { x: 0, y: "50%" } },
      ],
    },
  });
  // 不再需要单独处理鼠标悬停事件来显示连接桩，改为全局事件
  // console.log("创建的节点:", node.toJSON());

  // 手动触发一次连接桩显示
  graphInstance.trigger("node:mouseenter", { node });

  // 调试输出
  console.log("节点端口:", node.getPorts());

  // 选中新创建的节点
  graphInstance.cleanSelection();
  graphInstance.select(node);
  console.log("类图节点已添加:", node);
  return node;
}

export { handleDrop, createClassNode };
