<template>
  <div class="tasks-list">
    <h2>HPC监控</h2>
    <el-table :data="tableData">
      <el-table-column
        v-for="item in tableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        :width="item.width || 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.value === 'startTime'">
            {{ formatDateTime(row[item.value]) }}
          </span>
          <span v-if="item.value === 'status'">
            {{ row[item.value] === 0 ? "STOP" : "START" }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button @click="handleShow(row)" type="primary" size="small">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-right"
      v-model:current-page="pages.currentPage"
      v-model:page-size="pages.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      background
      size="small"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pages.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="visibleTask"
      title="任务详情"
      width="80%"
      @close="visibleTask = false"
    >
      <task-detail :taskId="currentTaskId" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { to, hpc_auth_list } from "@/api/index.js";
import { formatDateTime } from "@/utils/index.js";
import TaskDetail from "@/components/Task/TaskDetail.vue";

// Props - 接收外部传入的统计数据更新函数
const props = defineProps({
  onStatsUpdate: {
    type: Function,
    default: () => {},
  },
});

// 原始数据存储
const allTableData = ref([]);

// 分页配置
const pages = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 当前页显示的数据
const tableData = computed(() => {
  const start = (pages.currentPage - 1) * pages.pageSize;
  const end = start + pages.pageSize;
  return allTableData.value.slice(start, end);
});

// 表格列配置
const tableItems = ref([
  { label: "用户ID", value: "userId" },
  { label: "邮箱", value: "email" },
  { label: "HPC ID", value: "hpcId" },
  { label: "超算账号", value: "hpcAccount" },
  { label: "类型", value: "operType" },
  { label: "状态", value: "extension1" },
  { label: "HPC作业根路径", value: "extension2", width: 250 },
]);

// 任务详情弹窗
const visibleTask = ref(false);
const currentTaskId = ref(null);

// 查看任务详情
const handleShow = (row) => {
  window.open(
    `http://www.aicnic.cn/jobs-solver/#/weblogin?applicationname=solver&email=${row.email}&hpcid=${row.hpcId}&account=${row.hpcAccount}`,
    "_blank"
  );
};

// 页码改变处理
const handleCurrentChange = (page) => {
  pages.currentPage = page;
};

// 每页条数改变处理
const handleSizeChange = (size) => {
  pages.pageSize = size;
};

// 获取数据列表
const fetchList = async () => {
  const [err, res] = await to(() => hpc_auth_list());
  if (err) {
    ElMessage.error("获取数据失败");
    return;
  }

  // 存储所有数据
  allTableData.value = res.data || [];
  pages.total = allTableData.value.length;
  pages.currentPage = 1; // 重置到第一页

  // 计算统计数据并通知父组件
  const count = {
    start: allTableData.value.filter((item) => item.extension1 === "START")
      .length,
    stop: allTableData.value.filter((item) => item.extension1 === "STOP")
      .length,
    total: allTableData.value.length,
  };

  // 通知父组件更新统计数据
  props.onStatsUpdate(count);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchList();
});

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchList,
});
</script>

<style lang="scss" scoped>
.tasks-list {
  .pagination-right {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
