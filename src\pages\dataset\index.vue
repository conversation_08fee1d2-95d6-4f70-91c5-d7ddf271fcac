<template>
  <div class="dataset-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <h1>数据集</h1>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据集、模型、竞赛等"
            size="large"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container" style="margin-top: 20px">
      <el-row :gutter="24">
        <!-- 左侧筛选面板 -->
        <el-col :span="6">
          <div class="filter-panel">
            <!-- 快捷筛选 -->
            <div class="filter-section">
              <h3>快捷筛选</h3>
              <div class="quick-filters">
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'hot' }"
                  @click="setQuickFilter('hot')"
                >
                  热门
                </div>
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'new' }"
                  @click="setQuickFilter('new')"
                >
                  最新
                </div>
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'free' }"
                  @click="setQuickFilter('free')"
                >
                  免费
                </div>
              </div>
            </div>

            <!-- 任务类型 -->
            <div class="filter-section">
              <h3>任务类型</h3>
              <el-checkbox-group
                v-model="filters.taskTypes"
                class="filter-list"
              >
                <el-checkbox label="图像分类">图像分类</el-checkbox>
                <el-checkbox label="目标检测">目标检测</el-checkbox>
                <el-checkbox label="语义分割">语义分割</el-checkbox>
                <el-checkbox label="自然语言处理">自然语言处理</el-checkbox>
                <el-checkbox label="语音识别">语音识别</el-checkbox>
                <el-checkbox label="推荐系统">推荐系统</el-checkbox>
                <el-checkbox label="时间序列">时间序列</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 数据模态 -->
            <div class="filter-section">
              <h3>数据模态</h3>
              <el-checkbox-group
                v-model="filters.dataModals"
                class="filter-list"
              >
                <el-checkbox label="图像">图像</el-checkbox>
                <el-checkbox label="文本">文本</el-checkbox>
                <el-checkbox label="音频">音频</el-checkbox>
                <el-checkbox label="视频">视频</el-checkbox>
                <el-checkbox label="表格">表格</el-checkbox>
                <el-checkbox label="多模态">多模态</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 数据规模 -->
            <div class="filter-section">
              <h3>数据规模</h3>
              <el-checkbox-group
                v-model="filters.dataScale"
                class="filter-list"
              >
                <el-checkbox label="< 1K">< 1K</el-checkbox>
                <el-checkbox label="1K - 10K">1K - 10K</el-checkbox>
                <el-checkbox label="10K - 100K">10K - 100K</el-checkbox>
                <el-checkbox label="100K - 1M">100K - 1M</el-checkbox>
                <el-checkbox label="> 1M">> 1M</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 开源协议 -->
            <div class="filter-section">
              <h3>开源协议</h3>
              <el-checkbox-group v-model="filters.licenses" class="filter-list">
                <el-checkbox label="MIT">MIT</el-checkbox>
                <el-checkbox label="Apache-2.0">Apache-2.0</el-checkbox>
                <el-checkbox label="GPL-3.0">GPL-3.0</el-checkbox>
                <el-checkbox label="CC BY-4.0">CC BY-4.0</el-checkbox>
                <el-checkbox label="自定义">自定义</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 重置按钮 -->
            <div class="filter-actions">
              <el-button @click="resetFilters" text>
                <el-icon><RefreshRight /></el-icon>
                重置筛选
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 右侧内容区域 -->
        <el-col :span="18">
          <div class="content-area">
            <!-- 排序和视图切换 -->
            <div class="content-header">
              <div class="result-info">
                共 <span class="highlight">{{ total }}</span> 个数据集
              </div>
              <div class="header-actions">
                <div class="sort-select">
                  <span>排序：</span>
                  <el-select v-model="sortBy" size="small">
                    <el-option label="相关性" value="relevance" />
                    <el-option label="最新" value="newest" />
                    <el-option label="下载量" value="downloads" />
                  </el-select>
                </div>
              </div>
            </div>
            <!-- 数据集列表 -->
            <div class="dataset-list" v-loading="loading">
              <div
                v-for="dataset in datasetList"
                :key="dataset.id"
                class="dataset-card"
                @click="goToDetail(dataset.id)"
              >
                <div class="card-main">
                  <div class="card-left">
                    <div class="dataset-image">
                      <el-image
                        :src="dataset.image"
                        :alt="dataset.title"
                        width="100%"
                        height="100%"
                        fit="cover"
                      ></el-image>
                    </div>
                  </div>
                  <div class="card-content">
                    <div class="dataset-header">
                      <h3 class="dataset-title">{{ dataset.title }}</h3>
                      <div class="dataset-tags">
                        <el-tag size="small" type="primary">{{
                          dataset.category
                        }}</el-tag>
                        <el-tag size="small">{{ dataset.dataType }}</el-tag>
                        <el-tag
                          size="small"
                          v-if="dataset.isFree"
                          type="success"
                          >免费</el-tag
                        >
                      </div>
                    </div>
                    <p class="dataset-description">{{ dataset.description }}</p>
                  </div>
                </div>
                <div class="dataset-meta">
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ dataset.author }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    <span>{{ formatDate(dataset.updateTime) }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Download /></el-icon>
                    <span>{{ formatNumber(dataset.downloads) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-area">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[12, 24, 48]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<route lang="yaml">
meta:
  layout: front
</route>
<script setup>
import dayjs from "dayjs";

// 响应式数据
const loading = ref(false);
const searchKeyword = ref("");
const sortBy = ref("downloads");
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const quickFilter = ref("");

// 筛选条件
const filters = reactive({
  taskTypes: [],
  dataModals: [],
  dataScale: [],
  licenses: [],
});

// 数据集列表
const datasetList = ref([]);

// 模拟数据
const mockDatasets = [
  {
    id: 1,
    title: "ImageNet-1K 图像分类数据集",
    description:
      "包含1000个类别的大规模图像分类数据集，是计算机视觉领域的经典基准数据集，广泛用于图像分类、目标检测等任务的预训练。",
    category: "图像分类",
    dataType: "图像",
    author: "Stanford Vision Lab",
    updateTime: new Date("2024-01-15"),
    downloads: 125000,
    rating: 4.8,
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 2,
    title: "COCO 2017 目标检测数据集",
    description:
      "微软发布的大规模目标检测、分割和图像描述数据集，包含80个类别的目标，是目标检测任务的标准评测数据集。",
    category: "目标检测",
    dataType: "图像",
    author: "Microsoft",
    updateTime: new Date("2024-02-20"),
    downloads: 89000,
    rating: 4.9,
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 3,
    title: "BERT 中文语料库",
    description:
      "大规模中文预训练语料库，包含新闻、百科、论坛等多种文本类型，适用于中文自然语言处理任务的预训练。",
    category: "自然语言处理",
    dataType: "文本",
    author: "Google AI",
    updateTime: new Date("2024-03-10"),
    downloads: 67000,
    rating: 4.7,
    isFree: false,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 4,
    title: "Common Voice 语音识别数据集",
    description:
      "Mozilla发布的多语言语音识别数据集，包含多种语言的高质量语音数据，支持语音识别和语音合成任务。",
    category: "语音识别",
    dataType: "音频",
    author: "Mozilla",
    updateTime: new Date("2024-03-25"),
    downloads: 45000,
    rating: 4.6,
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
];

// 工具方法
const formatDate = (date) => {
  return dayjs(date).format("YYYY-MM-DD");
};

const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// 事件处理方法
const router = useRouter();
const goToDetail = (datasetId) => {
  router.push(`/dataset/detail/${datasetId}`);
};

const handleSearch = () => {
  console.log("搜索关键词:", searchKeyword.value);
  loadDatasets();
};

const setQuickFilter = (filter) => {
  quickFilter.value = quickFilter.value === filter ? "" : filter;
  loadDatasets();
};

const resetFilters = () => {
  filters.taskTypes = [];
  filters.dataModals = [];
  filters.dataScale = [];
  filters.licenses = [];
  quickFilter.value = "";
  loadDatasets();
};

const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  loadDatasets();
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
  loadDatasets();
};

const loadDatasets = async () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    datasetList.value = mockDatasets;
    total.value = mockDatasets.length;
    loading.value = false;
  }, 500);
};

// 初始化
onMounted(() => {
  loadDatasets();
});
</script>

<style lang="scss" scoped>
.dataset-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

// 顶部导航
.top-nav {
  background: white;
  border-bottom: 1px solid #e5e6eb;
  position: sticky;
  top: 0;
  z-index: 100;

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nav-left {
      .logo {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;

        img {
          width: 32px;
          height: 32px;
        }
      }
    }

    .nav-right {
      display: flex;
      gap: 12px;
    }
  }
}

// 搜索区域
.search-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 16px;
  color: white;

  .search-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 32px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .search-box {
      margin-bottom: 32px;

      .search-input {
        max-width: 600px;

        :deep(.el-input__wrapper) {
          border-radius: 50px;
          padding: 12px 20px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        :deep(.el-input-group__append) {
          border-radius: 0 50px 50px 0;
          background: #409eff;
          border-color: #409eff;

          .el-button {
            border: none;
            color: white;
          }
        }
      }
    }
  }
}

// 筛选面板
.filter-panel {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .filter-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f3f4f6;
    }

    .quick-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .filter-tag {
        padding: 6px 12px;
        border: 1px solid #d1d5db;
        border-radius: 16px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }

        &.active {
          background: #3b82f6;
          border-color: #3b82f6;
          color: white;
        }
      }
    }

    .filter-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      :deep(.el-checkbox) {
        margin: 0;

        .el-checkbox__label {
          font-size: 14px;
          color: #4b5563;
        }
      }
    }
  }

  .filter-actions {
    padding-top: 16px;
    border-top: 1px solid #f3f4f6;
  }
}

// 内容区域
.content-area {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f3f4f6;

    .result-info {
      font-size: 16px;
      color: #6b7280;

      .highlight {
        color: #3b82f6;
        font-weight: 600;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      span {
        width: 80px;
      }
      .sort-select {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #6b7280;
        width: 120px;
      }
    }
  }
  .dataset-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    .dataset-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      padding: 20px;
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      gap: 16px;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border-color: #3b82f6;
        cursor: pointer;
      }

      .card-main {
        display: flex;
        gap: 20px;

        .card-left {
          flex-shrink: 0;

          .dataset-image {
            width: 160px;
            height: 120px;
            border-radius: 8px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .card-content {
          flex: 1;
          min-width: 0; // 防止flex子元素溢出

          .dataset-header {
            margin-bottom: 12px;

            .dataset-title {
              font-size: 18px;
              font-weight: 600;
              color: #1f2937;
              margin: 0 0 8px 0;
              line-height: 1.3;
              // 标题最多一行
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .dataset-tags {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;
            }
          }

          .dataset-description {
            color: #6b7280;
            line-height: 1.5;
            margin: 0;
            font-size: 14px;
            // 描述最多两行
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-clamp: 2;
            overflow: hidden;
          }
        }
      }

      .dataset-meta {
        display: flex;
        align-items: center;
        gap: 20px;
        padding-top: 12px;
        border-top: 1px solid #f3f4f6;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #6b7280;

          .el-icon {
            font-size: 14px;
          }
        }
      }

      // 网格模式
      &.grid-mode {
        .grid-card {
          .card-image {
            width: 100%;
            height: 150px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .card-body {
            .card-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin: 0 0 8px 0;
            }
            .card-desc {
              color: #6b7280;
              font-size: 14px;
              line-height: 1.5;
              margin-bottom: 12px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              line-clamp: 2;
              overflow: hidden;
            }

            .card-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .card-meta {
                font-size: 12px;
                color: #9ca3af;

                span {
                  display: block;
                }
              }
            }
          }
        }
      }
    }

    // 网格布局
    &:has(.grid-mode) {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }
  }

  .pagination-area {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #f3f4f6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-section {
    padding: 40px 16px;

    .search-container {
      h1 {
        font-size: 32px;
      }

      .search-tabs {
        gap: 16px;
      }
    }
  }

  .main-container {
    margin: -20px 16px 0;

    .el-col:first-child {
      margin-bottom: 20px;
    }
  }
  .content-area {
    .content-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .dataset-list {
      grid-template-columns: 1fr;

      .dataset-card {
        .card-main {
          flex-direction: column;

          .card-left {
            align-self: center;
          }
        }

        .dataset-meta {
          justify-content: center;
          flex-wrap: wrap;
          gap: 12px;
        }
      }
    }
  }
}
</style>
