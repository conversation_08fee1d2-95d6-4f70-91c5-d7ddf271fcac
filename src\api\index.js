import http from "@/utils/request";

const env = import.meta.env.VITE_API_BASE_ENV || "dev"; // 环境变量，可根据实际情况修改

export const to = (fn) => {
  return fn()
    .then((data) => [null, data])
    .catch((err) => [err, null]);
};

// 用户登录
export const user_login = (params) => {
  return http.get(`/jobs-solver/api/${env}/user/login`, params);
};

// 退出登录
export const user_logout = () => {
  return http.delete(`/jobs-solver/api/${env}/user/logout`);
};

// 用户信息
export const user_info = () => {
  return http.get(`/jobs-solver/api/${env}/user/info`);
};

// HPC
export const hpc_list = (params) => {
  return http.get(`/jobs-solver/api/${env}/monitor/hpc`, params);
};

// hpc授权列表
export const hpc_auth_list = (params) => {
  return http.get(`/jobs-solver/api/${env}/uhpc/list`, params);
};

// 队列
export const queue_list = (params) => {
  return http.get(`/jobs-solver/api/${env}/monitor/partition`, params);
};

// 机时任务
export const machine_runtime_list = (params) => {
  return http.get(`/jobs-solver/api/${env}/statistics`, params);
};

// 工作列表
export const job_list = (params) => {
  return http.get(`/solver/api/${env}/monitor/joblist`, params);
};

// 工作详情
export const job_detail = (params) => {
  return http.get(`/solver/api/${env}/monitor/detail`, params);
};

// 取消工作
export const job_cancel = (params) => {
  return http.post(`/solver/api/${env}/monitor/joblist/cancel`, params);
};

// 创建任务
export const task_create = (params) => {
  return http.post(`/solver/api/${env}/task/newtask/oilgas/create`, params);
};

// 取消任务
export const task_cancel = (params) => {
  return http.post(`/solver/api/${env}/task/cancel`, params);
};

// 文件上传
export const file_upload = (params) => {
  return http.post(
    `/jobs-solver/api/${env}/files/chunk/upload2?token=${localStorage.getItem(
      "token"
    )}`,
    params
  );
};

// 文件输出
export const file_outfile = (params) => {
  return http.get(`/solver/api/${env}/files/analysis/outfile`, params);
};

// 总结
export const file_summary = (params) => {
  return http.get(`/solver/api/${env}/files/analysis/summary`, params);
};

//  删除文件
export const file_delete = (params) => {
  return http.delete(`/solver/api/${env}/files/browser/delete`, params);
};

// 文件列表
export const file_list = (params) => {
  return http.get(`/solver/api/${env}/files/browser/list`, params);
};

// 文件内容
export const file_content = (params) => {
  return http.get(`/solver/api/${env}/files/content`, params);
};

// 下载文件
export const file_download = (params) => {
  console.log(params);
  return http.download(
    `/solver/api/${env}/files/download/file3`,
    { fileFullName: params.fileFullName }, // 请求参数
    params.fileName // 文件名
  );
};
