<template>
  <div class="three-vtk-container">
    <div class="controls">
      <el-select
        v-model="selectedFile"
        @change="loadSelectedFile"
        placeholder="选择VTK文件"
        style="width: 200px"
      >
        <el-option
          v-for="file in vtkFiles"
          :key="file.value"
          :label="file.label"
          :value="file.value"
        />
      </el-select>

      <input
        type="file"
        @change="handleFileUpload"
        accept=".vtk"
        ref="fileInput"
        style="margin-left: 10px"
      />

      <el-button @click="resetCamera" type="primary" size="small">
        重置视角
      </el-button>

      <el-button @click="toggleWireframe" size="small">
        {{ wireframe ? "实体模式" : "线框模式" }}
      </el-button>
    </div>

    <div class="viewer-info">
      <span v-if="modelInfo.points > 0">
        点数: {{ modelInfo.points }} | 面数: {{ modelInfo.faces }}
      </span>
    </div>

    <div ref="threeContainer" class="three-viewer"></div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from "vue";
import * as THREE from "three";
import { VTKLoader } from "three/examples/jsm/loaders/VTKLoader.js";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";

export default {
  name: "ThreeVtkViewer",
  setup() {
    const threeContainer = ref(null);
    const fileInput = ref(null);
    const selectedFile = ref("/sphere.vtk");
    const wireframe = ref(false);
    const modelInfo = ref({ points: 0, faces: 0 });

    let scene = null;
    let camera = null;
    let renderer = null;
    let controls = null;
    let currentMesh = null;

    // VTK文件列表
    const vtkFiles = ref([
      { value: "/sphere.vtk", label: "Sphere - 球体" },
      { value: "/cube.vtk", label: "Cube - 立方体" },
      { value: "/cylinder.vtk", label: "Cylinder - 圆柱体" },
      { value: "/pyramid.vtk", label: "Pyramid - 金字塔" },
      { value: "/tetrahedron.vtk", label: "Tetrahedron - 四面体" },
      { value: "/triangle.vtk", label: "Triangle - 三角形" },
      { value: "/points-with-data.vtk", label: "Points with Data - 数据点" },
      { value: "/simple-point.vtk", label: "Simple Point - 简单点" },
    ]);

    // 初始化Three.js场景
    const initThreeJS = () => {
      if (!threeContainer.value) return;

      // 创建场景
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x1a1a1a);

      // 创建相机
      const container = threeContainer.value;
      camera = new THREE.PerspectiveCamera(
        75,
        container.clientWidth / container.clientHeight,
        0.1,
        1000
      );
      camera.position.set(5, 5, 5);

      // 创建渲染器
      renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
      });
      renderer.setSize(container.clientWidth, container.clientHeight);
      renderer.setPixelRatio(window.devicePixelRatio);
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;

      container.appendChild(renderer.domElement);

      // 添加控制器
      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controls.enableZoom = true;
      controls.enablePan = true;

      // 添加光源
      addLights();

      // 开始渲染循环
      animate();

      // 监听窗口大小变化
      window.addEventListener("resize", onWindowResize);
    };

    // 添加光源
    const addLights = () => {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
      scene.add(ambientLight);

      // 主光源
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(10, 10, 5);
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      scene.add(directionalLight);

      // 填充光
      const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
      fillLight.position.set(-5, 0, -5);
      scene.add(fillLight);
    };

    // 窗口大小调整
    const onWindowResize = () => {
      if (!camera || !renderer || !threeContainer.value) return;

      const container = threeContainer.value;
      camera.aspect = container.clientWidth / container.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(container.clientWidth, container.clientHeight);
    };

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);

      if (controls) {
        controls.update();
      }

      if (renderer && scene && camera) {
        renderer.render(scene, camera);
      }
    };

    // 清除当前模型
    const clearScene = () => {
      if (currentMesh) {
        scene.remove(currentMesh);

        // 清理几何体和材质
        if (currentMesh.geometry) {
          currentMesh.geometry.dispose();
        }
        if (currentMesh.material) {
          if (Array.isArray(currentMesh.material)) {
            currentMesh.material.forEach((material) => material.dispose());
          } else {
            currentMesh.material.dispose();
          }
        }

        currentMesh = null;
      }

      modelInfo.value = { points: 0, faces: 0 };
    };

    // 从URL加载VTK文件
    const loadVTKFromURL = async (url) => {
      if (!scene) return;

      try {
        clearScene();

        console.log("Loading VTK file:", url);

        const loader = new VTKLoader();

        loader.load(
          url,
          (geometry) => {
            try {
              // 计算法向量
              geometry.computeVertexNormals();

              // 计算边界框以居中模型
              geometry.computeBoundingBox();
              const box = geometry.boundingBox;
              const center = box.getCenter(new THREE.Vector3());
              geometry.translate(-center.x, -center.y, -center.z);

              // 创建材质
              const material = new THREE.MeshLambertMaterial({
                color: 0x00aaff,
                side: THREE.DoubleSide,
                wireframe: wireframe.value,
              });

              // 创建网格
              currentMesh = new THREE.Mesh(geometry, material);
              currentMesh.castShadow = true;
              currentMesh.receiveShadow = true;

              scene.add(currentMesh);

              // 更新模型信息
              modelInfo.value = {
                points: geometry.attributes.position.count,
                faces: geometry.index
                  ? geometry.index.count / 3
                  : geometry.attributes.position.count / 3,
              };

              // 重置相机
              resetCamera();

              console.log("VTK file loaded successfully");
            } catch (error) {
              console.error("Error processing VTK geometry:", error);
              alert(`处理VTK几何体失败: ${error.message}`);
            }
          },
          (progress) => {
            console.log("Loading progress:", progress);
          },
          (error) => {
            console.error("Error loading VTK file:", error);
            alert(`加载VTK文件失败: ${error.message}`);
          }
        );
      } catch (error) {
        console.error("Error in loadVTKFromURL:", error);
        alert(`加载VTK文件失败: ${error.message}`);
      }
    };

    // 从本地文件加载VTK
    const loadVTKFile = (file) => {
      if (!scene) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          clearScene();

          const loader = new VTKLoader();
          const geometry = loader.parse(event.target.result);

          // 计算法向量和边界框
          geometry.computeVertexNormals();
          geometry.computeBoundingBox();
          const box = geometry.boundingBox;
          const center = box.getCenter(new THREE.Vector3());
          geometry.translate(-center.x, -center.y, -center.z);

          // 创建材质和网格
          const material = new THREE.MeshLambertMaterial({
            color: 0x00aaff,
            side: THREE.DoubleSide,
            wireframe: wireframe.value,
          });

          currentMesh = new THREE.Mesh(geometry, material);
          currentMesh.castShadow = true;
          currentMesh.receiveShadow = true;

          scene.add(currentMesh);

          // 更新模型信息
          modelInfo.value = {
            points: geometry.attributes.position.count,
            faces: geometry.index
              ? geometry.index.count / 3
              : geometry.attributes.position.count / 3,
          };

          resetCamera();

          console.log("Local VTK file loaded successfully");
        } catch (error) {
          console.error("Error loading local VTK file:", error);
          alert(`加载本地VTK文件失败: ${error.message}`);
        }
      };

      reader.readAsArrayBuffer(file);
    };

    // 重置相机位置
    const resetCamera = () => {
      if (!currentMesh || !camera || !controls) return;

      // 计算模型的边界球
      const box = new THREE.Box3().setFromObject(currentMesh);
      const sphere = box.getBoundingSphere(new THREE.Sphere());

      const distance = sphere.radius * 3;
      camera.position.set(distance, distance, distance);
      camera.lookAt(0, 0, 0);

      controls.target.set(0, 0, 0);
      controls.update();
    };

    // 切换线框模式
    const toggleWireframe = () => {
      wireframe.value = !wireframe.value;

      if (currentMesh && currentMesh.material) {
        currentMesh.material.wireframe = wireframe.value;
      }
    };

    // 选择文件变化
    const loadSelectedFile = () => {
      loadVTKFromURL(selectedFile.value);
    };

    // 处理文件上传
    const handleFileUpload = (event) => {
      const file = event.target.files[0];
      if (file) {
        loadVTKFile(file);
      }
    };

    onMounted(() => {
      initThreeJS();
      // 延迟加载默认文件
      setTimeout(() => {
        loadSelectedFile();
      }, 100);
    });

    onBeforeUnmount(() => {
      window.removeEventListener("resize", onWindowResize);

      // 清理Three.js资源
      if (renderer) {
        renderer.dispose();
      }

      clearScene();
    });

    return {
      threeContainer,
      fileInput,
      selectedFile,
      wireframe,
      modelInfo,
      vtkFiles,
      handleFileUpload,
      loadSelectedFile,
      resetCamera,
      toggleWireframe,
    };
  },
};
</script>

<style scoped>
.three-vtk-container {
  width: 100%;
  height: 600px;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.controls {
  padding: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.viewer-info {
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 12px;
  color: #6c757d;
}

.three-viewer {
  width: 100%;
  height: calc(100% - 120px);
  background: #1a1a1a;
}

input[type="file"] {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
}

.el-button {
  height: 32px;
}
</style>
