<template>
  <div class="container-fluid front-header">
    <div class="container front-menu">
      <div class="logo">
        <img src="@/assets/images/logo.png" alt="Logo" />
      </div>
      <div class="front-menu-content">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-vertical"
          background-color="#1a59b0"
          text-color="#fff"
          active-text-color="#ffd04b"
          mode="horizontal"
        >
          <template v-for="nav in navs">
            <el-menu-item
              v-if="!nav.children || nav.children.length === 0"
              :key="nav.id"
              :index="nav.id"
              @click="handleClick(nav)"
              >{{ nav.label }}</el-menu-item
            >
            <el-sub-menu v-else :key="`sub-${nav.id}`" :index="nav.id">
              <template #title>
                <span>{{ nav.label }}</span>
              </template>
              <el-menu-item
                v-for="child in nav.children"
                :key="child.id"
                :index="child.id"
                @click="handleClick(child)"
              >
                {{ child.label }}
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
        <!-- <el-button type="default" circle size="small" @click="showLoginBox">
          <el-icon><Avatar /></el-icon>
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { user_logout } from "@/api/index";
import { userStore } from "@/stores/user";

const navs = shallowRef([
  { id: "1", label: "首页", path: "/", type: "route" },
  { id: "2", label: "数据集", path: "/dataset", type: "route" },
  { id: "3", label: "模型库", path: "/model", type: "route" },
  { id: "4", label: "资源池", path: "/resource", type: "route" },
  { id: "5", label: "分析工具", path: "/analysis", type: "route" },
  { id: "6", label: "工作流", path: "/workflow", type: "route" },
  {
    id: "7",
    label: "特色应用",
    path: "/feature",
    children: [
      { id: "7-1", label: "应用一", path: "/feature/app1", type: "route" },
      { id: "7-2", label: "应用二", path: "/feature/app2", type: "route" },
      { id: "7-3", label: "应用三", path: "/feature/app3", type: "route" },
    ],
  },
  { id: "8", label: "个人中心", path: "/account", type: "route" },
]);

// 在 setup 作用域内获取 router 和 route 实例
const router = useRouter();
const route = useRoute();
const activeIndex = ref("");

// 检查登录状态
const isLoggedIn = () => {
  const token =
    localStorage.getItem("token") || sessionStorage.getItem("token");
  return !!token;
};

// 需要登录的页面路径
const requireAuthPages = [
  "/dataset",
  "/model",
  "/resource",
  "/analysis",
  "/workflow",
  "/feature",
  "/profile",
];

const useUserStore = userStore();

const handleClick = async (row) => {
  try {
    if (!row) return;

    // 处理路由跳转
    if (row.type === "route") {
      // 检查是否需要登录
      const needAuth = requireAuthPages.some((authPage) =>
        row.path.startsWith(authPage)
      );
      if (needAuth && !isLoggedIn()) {
        // 未登录且访问需要权限的页面，跳转到登录页面并携带重定向参数
        router.push({
          path: "/login",
          query: { redirect: row.path },
        });
        return;
      }

      // 已登录或不需要登录，直接跳转
      router.push(row.path);
      return;
    }

    console.warn("未处理的导航类型:", row);
  } catch (error) {
    console.error("菜单点击处理出错:", error);
    ElMessage.error("操作失败，请重试");
  }
};

const showLoginBox = () => {
  // 跳转到登录页面，携带当前页面作为重定向参数
  router.push({
    path: "/login",
    query: { redirect: route.path },
  });
};

onMounted(() => {
  const path = route.path;
  const index = navs.value.findIndex((item) => item.path === path);
  if (index !== -1) {
    activeIndex.value = navs.value[index].id;
  }
});
</script>
<style lang="scss" scoped>
.front-header {
  background-color: #1a59b0;
  height: 100%;
  .front-menu {
    height: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .front-menu-content {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100%;
    }
    .el-menu-vertical {
      width: 100%;
      border-bottom: none;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
