<template>
  <div id="graph-container" @dragover="handleDragOver" @drop="handleDrop"></div>
</template>

<script setup>
import { onMounted } from "vue";
import { Graph, Shape, ObjectExt, Cell } from "@antv/x6";
import { Transform } from "@antv/x6-plugin-transform";
import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { History } from "@antv/x6-plugin-history";
import insertCss from "insert-css";
import { handleDrop as handleDropUtil } from "./handleDrop.js";

const emits = defineEmits(["graphReady"]);
let graphInstance = null;

// 处理拖拽经过画布上方事件
function handleDragOver(event) {
  event.preventDefault();
  event.dataTransfer.dropEffect = "copy";
}

// 处理拖拽放置事件
function handleDrop(event) {
  try {
    // 使用独立的处理函数进行处理
    handleDropUtil(event, graphInstance);
  } catch (error) {
    console.error("处理拖拽数据失败:", error);
  }
}

onMounted(() => {
  // 初始化画布
  const graph = new Graph({
    container: document.getElementById("graph-container"),
    grid: true,
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 3,
    },
    // 限制节点拖动范围为画布区域
    translating: {
      restrict: true,
    },
    connecting: {
      router: "manhattan",
      connector: { name: "rounded", args: { radius: 8 } },
      anchor: "center", // 使用中心作为锚点
      connectionPoint: "boundary", // 改为边界连接点
      allowBlank: false,
      allowLoop: false, // 不允许自环
      allowNode: false, // 不允许连到节点本身，只能连到端口
      allowEdge: false, // 不允许连到边
      highlight: true, // 高亮显示
      snap: { radius: 20 },
      createEdge() {
        try {
          const edge = new Shape.Edge({
            shape: "edge",
            attrs: {
              line: {
                stroke: "#A2B1C3",
                strokeWidth: 2,
                targetMarker: { name: "block", width: 12, height: 8 },
              },
            },
            zIndex: 0,
          });
          return edge;
        } catch (error) {
          console.error("创建边时出错:", error);
          return {
            shape: "edge",
            attrs: {
              line: {
                stroke: "#A2B1C3",
                strokeWidth: 2,
              },
            },
          };
        }
      },
      validateConnection({
        sourceMagnet,
        targetMagnet,
        sourceView,
        targetView,
        sourcePort,
        targetPort,
      }) {
        // 必须连接到端口
        if (!sourceMagnet || !targetMagnet) {
          return false;
        }

        // 不能连接到自己
        if (sourceView === targetView) {
          return false;
        }

        console.log("连接验证:", {
          sourceMagnet,
          targetMagnet,
          sourcePort,
          targetPort,
        });

        return true;
      },
    },
    highlighting: {
      magnetAdsorbed: {
        name: "stroke",
        args: { attrs: { fill: "#5F95FF", stroke: "#5F95FF" } },
      },
    },
  });
  // 注册插件
  graph
    .use(new Transform({ resizing: true, rotating: true }))
    .use(new Selection({ rubberband: true, showNodeSelectionBox: true }))
    .use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History()); // 快捷键与事件
  graph.bindKey(["meta+c", "ctrl+c"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) graph.copy(cells);
    return false;
  });
  graph.bindKey(["meta+x", "ctrl+x"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) graph.cut(cells);
    return false;
  });
  // 保存 graph 实例以便在拖放事件中使用
  graphInstance = graph;

  // 通知父组件画布已准备好
  emits("graphReady", graph);
  graph.bindKey(["meta+v", "ctrl+v"], () => {
    if (!graph.isClipboardEmpty()) {
      const cells = graph.paste({ offset: 32 });
      graph.cleanSelection();
      graph.select(cells);
    }
    return false;
  });
  graph.bindKey(["meta+z", "ctrl+z"], () => {
    if (graph.canUndo()) graph.undo();
    return false;
  });
  graph.bindKey(["meta+shift+z", "ctrl+shift+z"], () => {
    if (graph.canRedo()) graph.redo();
    return false;
  });
  graph.bindKey(["meta+a", "ctrl+a"], () => {
    const nodes = graph.getNodes();
    if (nodes) graph.select(nodes);
  });
  graph.bindKey("backspace", () => {
    const cells = graph.getSelectedCells();
    if (cells.length) graph.removeCells(cells);
  });
  graph.bindKey(["ctrl+1", "meta+1"], () => {
    const zoom = graph.zoom();
    if (zoom < 1.5) graph.zoom(0.1);
  });
  graph.bindKey(["ctrl+2", "meta+2"], () => {
    const zoom = graph.zoom();
    if (zoom > 0.5) graph.zoom(-0.1);
  });
  // 连接桩显示/隐藏
  const showPorts = (ports, show) => {
    for (let i = 0, len = ports.length; i < len; i += 1) {
      ports[i].style.visibility = show ? "visible" : "hidden";
    }
  };
  graph.on("node:mouseenter", () => {
    const container = document.getElementById("graph-container");
    const ports = container.querySelectorAll(".x6-port-body");
    showPorts(ports, true);
  });
  graph.on("node:mouseleave", () => {
    const container = document.getElementById("graph-container");
    const ports = container.querySelectorAll(".x6-port-body");
    showPorts(ports, false);
  });
  // 兼容所有 X6 版本，防止重复注册自定义节点
  if (typeof window !== "undefined") {
    window.__x6CustomNodes = window.__x6CustomNodes || {};
  }
  const customNodes = [
    {
      name: "class",
      config: {
        inherit: "rect",
        markup: [
          {
            tagName: "rect",
            selector: "body",
          },
          {
            tagName: "rect",
            selector: "name-rect",
          },
          {
            tagName: "rect",
            selector: "attrs-rect",
          },
          {
            tagName: "rect",
            selector: "methods-rect",
          },
          {
            tagName: "text",
            selector: "name-text",
          },
          {
            tagName: "text",
            selector: "attrs-text",
          },
          {
            tagName: "text",
            selector: "methods-text",
          },
        ],
        attrs: {
          rect: {
            width: 160,
          },
          body: {
            stroke: "#fff",
          },
          "name-rect": {
            fill: "#5f95ff",
            stroke: "#fff",
            strokeWidth: 0.5,
          },
          "attrs-rect": {
            fill: "#eff4ff",
            stroke: "#fff",
            strokeWidth: 0.5,
          },
          "methods-rect": {
            fill: "#eff4ff",
            stroke: "#fff",
            strokeWidth: 0.5,
          },
          "name-text": {
            ref: "name-rect",
            refY: 0.5,
            refX: 0.5,
            textAnchor: "middle",
            fontWeight: "bold",
            fill: "#fff",
            fontSize: 12,
          },
          "attrs-text": {
            ref: "attrs-rect",
            refY: 0.5,
            refX: 5,
            textAnchor: "left",
            fill: "black",
            fontSize: 10,
          },
          "methods-text": {
            ref: "methods-rect",
            refY: 0.5,
            refX: 5,
            textAnchor: "left",
            fill: "black",
            fontSize: 10,
          },
        },
        propHooks(meta) {
          const { name, dataFiles, methods, ...others } = meta;

          if (!(name && dataFiles && methods)) {
            return meta;
          }

          const rects = [
            { type: "name", text: name },
            { type: "attrs", text: dataFiles },
            { type: "methods", text: methods },
          ];

          let offsetY = 0;
          rects.forEach((rect) => {
            const height = rect.text.length * 12 + 16;
            ObjectExt.setByPath(
              others,
              `attrs/${rect.type}-text/text`,
              rect.text.join("\n")
            );
            ObjectExt.setByPath(
              others,
              `attrs/${rect.type}-rect/height`,
              height
            );
            ObjectExt.setByPath(
              others,
              `attrs/${rect.type}-rect/transform`,
              "translate(0," + offsetY + ")"
            );
            offsetY += height;
          });

          others.size = { width: 160, height: offsetY };

          return others;
        },
      },
    },
  ];
  customNodes.forEach((node) => {
    if (typeof window === "undefined" || !window.__x6CustomNodes[node.name]) {
      try {
        // 确保 config 中包含 shape 属性
        const config = {
          ...node.config,
          shape: node.name, // 将节点名称作为 shape 属性
        };

        Graph.registerNode(node.name, config, true);

        if (typeof window !== "undefined") {
          window.__x6CustomNodes[node.name] = true;
        }
        console.log(`成功注册节点: ${node.name}`);
      } catch (e) {
        console.warn(`注册节点 ${node.name} 时出错:`, e);
        // 忽略重复注册异常
      }
    }
  }); // 添加样式
  insertCss(`
    #graph-container {
      width: 100%;
      height: 100%;
    }
    .x6-widget-transform, .x6-widget-selection-box, .x6-widget-selection-inner {
      border: 1px solid #239edd;
    }
    .x6-port-body {
      visibility: visible;
      width: 12px;
      height: 12px;
      z-index: 10;
    }
    .x6-port-body:hover {
      transform: scale(1.2);
    }
  `);
  graphInstance = graph;
  emits("graphReady", graph);

  graph.on("node:click", ({ node, e }) => {
    emits("nodeClick", node.getData());
  });

  // 添加连接相关的事件监听
  graph.on("edge:connected", ({ edge }) => {
    console.log("连接已建立:", edge);
  });

  graph.on("edge:removed", ({ edge }) => {
    console.log("连接已删除:", edge);
  });

  graph.on("connection:valid", ({ edge }) => {
    console.log("有效连接:", edge);
  });

  graph.on("connection:invalid", ({ edge }) => {
    console.log("无效连接:", edge);
  });

  // 端口事件
  graph.on("node:port:click", ({ node, port }) => {
    console.log("点击端口:", node.id, port);
  });
});
defineExpose({ getGraph: () => graphInstance });
</script>

<style scoped>
#graph-container {
  width: 100%;
  height: 100%;
}
</style>
