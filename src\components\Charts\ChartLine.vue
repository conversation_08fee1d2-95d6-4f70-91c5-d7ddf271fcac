<template>
  <div class="chart-container">
    <div class="charts-grid">
      <div
        v-for="(metric, index) in availableMetrics"
        :key="metric"
        :ref="(el) => setChartRef(el, index)"
        class="chart-item"
        :style="{
          width: getChartItemWidth(),
          height: getChartItemHeight(),
        }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onUnmounted, watch } from "vue";

const props = defineProps({
  // 图表数据，格式为 { TIME: [...], 指标1: [...], 指标2: [...] }
  data: {
    type: Object,
    default: () => ({}),
  },
  // 图表高度（用于计算单个图表的高度）
  chartHeight: {
    type: String,
    default: "400px",
  },
  // 显示的指标（可选，如果不传则自动根据data的key生成）
  metrics: {
    type: Array,
    default: () => [],
  },
  // 排除的字段（不显示在图表中）
  excludeKeys: {
    type: Array,
    default: () => ["TIME", "time"],
  },
});

const chartRefs = ref([]);
let chartInstances = [];
const availableMetrics = ref([]);

// 设置图表ref的方法
const setChartRef = (el, index) => {
  if (el) {
    chartRefs.value[index] = el;
    console.log(`设置图表ref ${index}:`, el);
  }
};

// 默认数据 - 新格式
const defaultData = {
  TIME: [1.0, 3.0, 7.0, 15.0, 31.0, 62.0, 93.0, 124.0, 155.0],
  FPR: [
    392.234, 391.742, 390.758, 388.794, 384.882, 377.364, 369.984, 362.709,
    355.537,
  ],
  Volume: [
    5999980, 5999830, 5999550, 5998990, 5997870, 5995720, 5993600, 5991500,
    5989440,
  ],
  FOPR: [
    300.164, 300.0, 300.0, 300.001, 300.006, 300.02, 300.017, 300.017, 300.015,
  ],
  FGPR: [
    14601.6, 14593.7, 14593.7, 14593.7, 14593.9, 14594.6, 14594.5, 14594.4,
    14594.4,
  ],
  FWPR: [
    0.0001, 0.000183, 0.000302, 0.000533, 0.000988, 0.001849, 0.002695, 0.00352,
    0.004325,
  ],
  FGIR: [0, 0, 0, 0, 0, 0, 0, 0, 0],
};

// 指标配置
const metricsConfig = {
  FPR: { name: "压力 (PSIA)", color: "#ff6b6b" },
  Volume: { name: "体积 (Ft³)", color: "#4ecdc4" },
  FOPR: { name: "油产量 (STB/DAY)", color: "#45b7d1" },
  FGPR: { name: "气产量 (MSCF/DAY)", color: "#96ceb4" },
  FWPR: { name: "水产量 (STB/DAY)", color: "#ffeaa7" },
  FGIR: { name: "注气量 (MSCF/DAY)", color: "#dda0dd" },
};

// 颜色数组，用于自动生成颜色
const defaultColors = [
  "#ff6b6b",
  "#4ecdc4",
  "#45b7d1",
  "#96ceb4",
  "#ffeaa7",
  "#dda0dd",
  "#74b9ff",
  "#fd79a8",
  "#fdcb6e",
  "#6c5ce7",
  "#00b894",
  "#e17055",
  "#a29bfe",
  "#ffeaa7",
  "#81ecec",
  "#fab1a0",
  "#00cec9",
  "#e84393",
];

// 获取时间字段名
const getTimeField = (data) => {
  return data.TIME ? "TIME" : data.time ? "time" : null;
};

// 获取可显示的指标列表
const getAvailableMetrics = (data) => {
  if (!data || typeof data !== "object") return [];

  const timeField = getTimeField(data);
  if (!timeField) return [];

  // 如果用户指定了metrics，使用用户指定的
  if (props.metrics && props.metrics.length > 0) {
    return props.metrics.filter(
      (metric) =>
        data[metric] &&
        Array.isArray(data[metric]) &&
        data[metric].length === data[timeField].length
    );
  }

  // 否则自动获取所有可用的指标（排除时间字段和用户指定的排除字段）
  const excludeFields = [...props.excludeKeys];
  return Object.keys(data).filter(
    (key) =>
      !excludeFields.includes(key) &&
      data[key] &&
      Array.isArray(data[key]) &&
      data[key].length === data[timeField].length
  );
};

// 计算每个图表项的宽度
const getChartItemWidth = () => {
  const count = availableMetrics.value.length;
  if (count <= 2) return "100%";
  return "calc(50% - 40px)"; // 减去间距
};

// 计算每个图表项的高度
const getChartItemHeight = () => {
  const count = availableMetrics.value.length;
  const baseHeight = parseInt(props.chartHeight) || 400;
  if (count <= 2) return `${Math.min(baseHeight, 400)}px`;
  if (count <= 4) return `${Math.min(baseHeight * 0.8, 350)}px`;
  return `${Math.min(baseHeight * 0.6, 300)}px`;
};

// 获取指标配置
const getMetricConfig = (metric, index) => {
  // 如果有预定义配置，使用预定义配置
  if (metricsConfig[metric]) {
    return metricsConfig[metric];
  }

  // 否则生成默认配置，创建更友好的中文名称
  const generateFriendlyName = (key) => {
    // 常见的英文缩写映射
    const nameMap = {
      FPR: "压力",
      FOPR: "油产量",
      FGPR: "气产量",
      FWPR: "水产量",
      FGIR: "注气量",
      Volume: "体积",
      volume: "体积",
      pressure: "压力",
      temperature: "温度",
      flow: "流量",
      rate: "速率",
      prod: "产量",
      injection: "注入量",
    };

    // 如果有直接映射，返回映射值
    if (nameMap[key]) {
      return nameMap[key];
    }

    // 否则返回原key，但首字母大写
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  return {
    name: generateFriendlyName(metric),
    color: defaultColors[index % defaultColors.length],
  };
};

// 数据验证函数
const validateDataFormat = (data) => {
  if (!data || typeof data !== "object") return false;

  const timeField = getTimeField(data);
  if (
    !timeField ||
    !Array.isArray(data[timeField]) ||
    data[timeField].length === 0
  ) {
    return false;
  }

  // 检查至少有一个可用的指标数据
  const availableMetrics = getAvailableMetrics(data);
  return availableMetrics.length > 0;
};

// 初始化图表
const initChart = () => {
  // 清理现有的图表实例
  disposeCharts();

  // 先计算可用指标并更新响应式数据
  let chartData = defaultData;
  if (validateDataFormat(props.data)) {
    chartData = props.data;
  }

  const metrics = getAvailableMetrics(chartData);
  availableMetrics.value = metrics;

  console.log("初始化图表");
  console.log("可用指标:", metrics);

  // 等待Vue更新DOM后再初始化图表
  setTimeout(() => {
    // 为每个指标创建独立的图表实例
    chartInstances = [];
    console.log("chartRefs.value 长度:", chartRefs.value.length);

    chartRefs.value.forEach((ref, index) => {
      if (ref) {
        console.log(`初始化图表 ${index}:`, ref);
        const instance = echarts.init(ref);
        chartInstances.push(instance);
      } else {
        console.log(`图表 ${index} 的 ref 为空`);
      }
    });

    console.log("图表实例数量:", chartInstances.length);
    updateChart();
  }, 100);
};

// 清理图表实例
const disposeCharts = () => {
  chartInstances.forEach((instance) => {
    if (instance) {
      instance.dispose();
    }
  });
  chartInstances = [];

  // 清理refs数组
  chartRefs.value = [];
};

// 更新图表
const updateChart = () => {
  // 判断数据源格式，优先使用传入的数据，否则使用默认数据
  let chartData = defaultData;

  // 如果传入了数据并且格式正确，使用传入的数据
  if (validateDataFormat(props.data)) {
    chartData = props.data;
  } else if (Object.keys(props.data).length > 0) {
    console.warn(
      "传入的数据格式不正确，应为 { TIME: [1,2,3], 指标1: [...], 指标2: [...] } 格式，使用默认数据"
    );
  }

  const timeField = getTimeField(chartData);
  const timeData = chartData[timeField];

  // 获取可用的指标列表
  const metrics = getAvailableMetrics(chartData);
  availableMetrics.value = metrics;

  console.log("可用指标:", metrics);

  // 为每个指标生成独立图表
  metrics.forEach((metric, index) => {
    if (chartInstances[index]) {
      const config = getMetricConfig(metric, index);
      const singleOption = createSingleChart(
        chartData,
        metric,
        config,
        timeData
      );
      chartInstances[index].setOption(singleOption, true);
    }
  });
};

// 创建单个图表配置
const createSingleChart = (chartData, metric, config, timeData) => {
  return {
    title: {
      text: `${config.name} 趋势图`,
      left: "center",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#333",
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "#333",
      textStyle: {
        color: "#fff",
      },
      formatter: (params) => {
        const param = params[0];
        return `时间: ${param.axisValue} 天<br/>${config.name}: ${param.value}`;
      },
    },
    grid: {
      left: "15%",
      right: "15%",
      top: "25%",
      bottom: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      name: "时间 (天)",
      nameLocation: "middle",
      nameGap: 20,
      data: timeData,
      axisLine: {
        lineStyle: {
          color: "#666",
        },
      },
      axisLabel: {
        color: "#666",
        fontSize: 10,
      },
    },
    yAxis: {
      type: "value",
      name: config.name,
      axisLine: {
        show: true,
        lineStyle: {
          color: config.color,
        },
      },
      axisLabel: {
        color: config.color,
        fontSize: 10,
        formatter: (value) => {
          // 动态格式化数值
          if (Math.abs(value) >= 1000000) {
            return (value / 1000000).toFixed(1) + "M";
          }
          if (Math.abs(value) >= 1000) {
            return (value / 1000).toFixed(1) + "K";
          }
          return value < 1 ? value.toFixed(4) : value.toFixed(1);
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#e8e8e8",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: config.name,
        type: "line",
        data: chartData[metric],
        lineStyle: {
          color: config.color,
          width: 2,
        },
        itemStyle: {
          color: config.color,
        },
        symbol: "circle",
        symbolSize: 4,
        smooth: true,
      },
    ],
    dataZoom: [
      {
        type: "slider",
        start: 0,
        end: 100,
        height: 15,
        bottom: 5,
      },
    ],
  };
};

// 响应式调整
const handleResize = () => {
  chartInstances.forEach((instance) => {
    if (instance) {
      instance.resize();
    }
  });
};

onMounted(() => {
  // 使用更长的延迟确保DOM元素已经渲染完成
  setTimeout(() => {
    initChart();
    window.addEventListener("resize", handleResize);
  }, 200);
});

onUnmounted(() => {
  disposeCharts();
  window.removeEventListener("resize", handleResize);
});

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart();
  },
  { deep: true }
);

watch(
  () => props.metrics,
  () => {
    updateChart();
  },
  { deep: true }
);

watch(
  () => props.excludeKeys,
  () => {
    updateChart();
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  padding: 20px;
}

.charts-grid {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
}

.chart-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

// 响应式布局
@media (max-width: 768px) {
  .charts-grid {
    flex-direction: column;
    gap: 15px;
  }

  .chart-item {
    width: 100% !important;
    min-height: 250px;
  }
}

@media (min-width: 769px) and (max-width: 1200px) {
  .chart-item {
    min-height: 300px;
  }
}
</style>
