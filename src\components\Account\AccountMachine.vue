<template>
  <div>
    <!-- 筛选条件 -->
    <div class="filter-container">
      <div class="filter-wrapper">
        <div class="filter-item">
          <el-input
            v-model="filterForm.hpcId"
            placeholder="请输入HPC ID"
            clearable
            style="width: 250px"
          >
            <template #append>
              <el-select
                v-model="filterForm.hpcType"
                placeholder="类型"
                style="width: 80px"
              >
                <el-option label="ID" value="id" />
                <el-option label="名称" value="name" />
                <el-option label="编号" value="code" />
              </el-select>
            </template>
          </el-input>
        </div>

        <div class="filter-item">
          <label class="filter-label">起始时间:</label>
          <el-date-picker
            v-model="filterForm.startTime"
            type="datetime"
            placeholder="选择起始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 200px"
          />
        </div>

        <div class="filter-item">
          <label class="filter-label">结束时间:</label>
          <el-date-picker
            v-model="filterForm.endTime"
            type="datetime"
            placeholder="选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 200px"
          />
        </div>

        <div class="filter-item">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-table :data="runtimeData" style="width: 100%" v-loading="loading">
      <el-table-column
        label="序号"
        type="index"
        width="60"
        fixed
        :index="
          (index) =>
            (pagination.currentPage - 1) * pagination.pageSize + index + 1
        "
      ></el-table-column>
      <el-table-column
        v-for="item in tableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        :width="item.width || 'auto'"
        :formatter="(row) => formatData(row, item)"
      ></el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import dayjs from "dayjs";
import { to, machine_runtime_list } from "@/api/index";

// 表格列配置
const tableItems = ref([
  // { label: "用户ID", value: "id" },
  { label: "邮箱", value: "email", width: "160px" },
  { label: "任务ID", value: "taskId", width: "240px" },
  { label: "HPC ID", value: "hpcId" },
  { label: "超算账号", value: "extension1" },
  { label: "作业ID", value: "jobId" },
  { label: "Gres", value: "extension2" },
  { label: "起始时间", value: "startTime", width: "180px" },
  { label: "运行时间", value: "runTime" },
  // { label: "总时间", value: "seconds" },
]);

// 筛选表单
const filterForm = ref({
  hpcId: "",
  hpcType: "id", // 新增：HPC查询类型
  startTime: "",
  endTime: "",
});

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 数据相关
const runtimeData = ref([]);
const loading = ref(false);

// 存储所有数据用于前端分页
const allRuntimeData = ref([]);

/**
 * 获取机时任务列表
 * @param {Object} params - 查询参数
 */
const fetchMachineRuntimeList = async (params = {}) => {
  loading.value = true;
  try {
    // 构造请求参数
    const requestParams = {
      ...filterForm.value,
      ...params,
    };

    // 时间参数转换为时间戳（毫秒）
    if (requestParams.startTime) {
      requestParams.startTime = dayjs(requestParams.startTime).valueOf();
    }
    if (requestParams.endTime) {
      requestParams.endTime = dayjs(requestParams.endTime).valueOf();
    }

    // 移除空值参数
    Object.keys(requestParams).forEach((key) => {
      if (
        requestParams[key] === "" ||
        requestParams[key] === null ||
        requestParams[key] === undefined
      ) {
        delete requestParams[key];
      }
    });

    const [err, res] = await to(() => machine_runtime_list(requestParams));
    if (err) {
      console.error("获取机时任务列表失败:", err);
      return;
    }

    console.log("机时任务列表数据:", res.data);

    // 处理返回的数据
    let dataList = [];
    if (res.data && Array.isArray(res.data.list)) {
      dataList = res.data.list;
    } else if (Array.isArray(res.data)) {
      dataList = res.data;
    }

    // 存储所有数据并进行前端分页
    allRuntimeData.value = dataList;
    pagination.value.total = dataList.length;
    updateDisplayData();
  } catch (error) {
    console.error("获取机时任务列表异常:", error);
  } finally {
    loading.value = false;
  }
};

/**
 * 更新显示数据（前端分页）
 */
const updateDisplayData = () => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  runtimeData.value = allRuntimeData.value.slice(start, end);
};

/**
 * 格式化表格数据
 * @param {Object} row - 行数据
 * @param {Object} item - 列配置
 * @returns {String} 格式化后的值
 */
const formatData = (row, item) => {
  const value = row[item.value];

  // 特殊处理时间格式
  if (item.value === "startTime" && value) {
    return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
  }

  return value || "-";
};

/**
 * 查询处理
 */
const handleSearch = () => {
  pagination.value.currentPage = 1; // 重置到第一页
  fetchMachineRuntimeList();
};

/**
 * 重置筛选条件
 */
const handleReset = () => {
  filterForm.value = {
    hpcId: "",
    hpcType: "id",
    startTime: "",
    endTime: "",
  };
  pagination.value.currentPage = 1;
  fetchMachineRuntimeList();
};

/**
 * 分页大小改变
 * @param {Number} val - 新的页面大小
 */
const handleSizeChange = (val) => {
  pagination.value.pageSize = val;
  pagination.value.currentPage = 1;
  updateDisplayData();
};

/**
 * 当前页改变
 * @param {Number} val - 新的当前页
 */
const handleCurrentChange = (val) => {
  pagination.value.currentPage = val;
  updateDisplayData();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchMachineRuntimeList();
});
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  min-width: fit-content;
}

.filter-form {
  .el-form-item {
    margin-bottom: 0;
    margin-right: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

.pagination-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
