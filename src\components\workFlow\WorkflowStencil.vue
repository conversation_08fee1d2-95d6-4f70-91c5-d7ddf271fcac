<template>
  <div class="workflow-stencil">
    <!-- <div class="stencil-header">
      <h3 class="panel-title">节点模板</h3>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索节点..."
        size="small"
        prefix-icon="Search"
        clearable
        class="search-input"
      />
    </div> -->

    <el-scrollbar height="calc(100vh - 160px)" class="stencil-content">
      <div
        v-for="category in filteredCategories"
        :key="category.id"
        class="node-category"
      >
        <div class="category-header" @click="toggleCategory(category.id)">
          <el-icon :class="{ rotated: category.expanded }">
            <ArrowRight />
          </el-icon>
          <span class="category-title">{{ category.name }}</span>
          <span class="node-count">({{ category.nodes.length }})</span>
        </div>

        <div v-show="category.expanded" class="category-content">
          <div
            v-for="node in category.nodes"
            :key="node.id"
            class="node-template"
            :class="{ dragging: draggingNodeId === node.id }"
            draggable="true"
            @dragstart="handleDragStart($event, node)"
            @dragend="handleDragEnd"
          >
            <div class="node-preview">
              <div
                class="node-icon"
                :style="{
                  backgroundColor: node.color,
                  color: node.textColor || '#fff',
                }"
              >
                <el-icon :size="20">
                  <component :is="node.icon" />
                </el-icon>
              </div>
              <div class="node-info">
                <div class="node-name">{{ node.name }}</div>
                <div class="node-description">{{ node.description }}</div>
              </div>
            </div>

            <!-- 节点端口预览 -->
            <div class="node-ports" v-if="node.ports && node.ports.length > 0">
              <div class="ports-section">
                <div
                  v-for="port in node.ports.filter((p) => p.type === 'input')"
                  :key="port.id"
                  class="port-item input-port"
                >
                  <div class="port-dot"></div>
                  <span class="port-label">{{ port.label }}</span>
                </div>
              </div>
              <div class="ports-section">
                <div
                  v-for="port in node.ports.filter((p) => p.type === 'output')"
                  :key="port.id"
                  class="port-item output-port"
                >
                  <span class="port-label">{{ port.label }}</span>
                  <div class="port-dot"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";

// 组件事件
const emit = defineEmits(["node-drag-start"]);

// 响应式数据
const searchKeyword = ref("");
const draggingNodeId = ref(null);

// 节点分类数据
const nodeCategories = ref([
  {
    id: "model",
    name: "模型",
    expanded: true,
    nodes: [
      {
        id: "component-model",
        name: "组分模型",
        description: "多组分流体模拟模型",
        type: "model",
        icon: "Cpu",
        color: "#8B5A96",
        textColor: "#fff",
        width: 120,
        height: 60,
        ports: [
          { id: "input", type: "input", label: "流体数据" },
          { id: "output", type: "output", label: "组分结果" },
        ],
        properties: {
          components: [],
          temperature: 25,
          pressure: 1.0,
          phases: ["gas", "oil", "water"],
        },
      },
      {
        id: "black-oil-model",
        name: "黑油模型",
        description: "黑油PVT模型",
        type: "model",
        icon: "DataAnalysis",
        color: "#2C3E50",
        textColor: "#fff",
        width: 120,
        height: 60,
        ports: [
          { id: "input", type: "input", label: "PVT数据" },
          { id: "output", type: "output", label: "黑油参数" },
        ],
        properties: {
          oilGravity: 35,
          gasGravity: 0.65,
          waterSalinity: 0,
          correlations: "Standing",
        },
      },
      {
        id: "thermal-model",
        name: "热采模型",
        description: "热力采油模拟模型",
        type: "model",
        icon: "Monitor",
        color: "#E74C3C",
        textColor: "#fff",
        width: 120,
        height: 60,
        ports: [
          { id: "input", type: "input", label: "热力参数" },
          { id: "output", type: "output", label: "采收结果" },
        ],
        properties: {
          injectionTemperature: 350,
          steamQuality: 0.8,
          heatLoss: 0.1,
          thermalConductivity: 2.0,
        },
      },
      {
        id: "isothermal-model",
        name: "等温模型",
        description: "等温流体流动模型",
        type: "model",
        icon: "Platform",
        color: "#17A2B8",
        textColor: "#fff",
        width: 120,
        height: 60,
        ports: [
          { id: "input", type: "input", label: "流体属性" },
          { id: "output", type: "output", label: "流动结果" },
        ],
        properties: {
          temperature: 60,
          viscosity: 1.0,
          permeability: 100,
          porosity: 0.2,
        },
      },
    ],
  },
]);

// 计算属性 - 过滤后的分类
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return nodeCategories.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return nodeCategories.value
    .map((category) => ({
      ...category,
      nodes: category.nodes.filter(
        (node) =>
          node.name.toLowerCase().includes(keyword) ||
          node.description.toLowerCase().includes(keyword)
      ),
    }))
    .filter((category) => category.nodes.length > 0);
});

// 方法
const toggleCategory = (categoryId) => {
  const category = nodeCategories.value.find((cat) => cat.id === categoryId);
  if (category) {
    category.expanded = !category.expanded;
  }
};

const handleDragStart = (event, node) => {
  draggingNodeId.value = node.id;

  // 设置拖拽数据
  const dragData = {
    type: "workflow-node",
    nodeTemplate: node,
  };

  event.dataTransfer.setData("application/json", JSON.stringify(dragData));
  event.dataTransfer.effectAllowed = "copy";

  // 触发父组件事件
  emit("node-drag-start", node);

  console.log("开始拖拽节点:", node.name);
};

const handleDragEnd = () => {
  draggingNodeId.value = null;
};

// 生命周期
onMounted(() => {
  console.log("工作流节点模板面板已挂载");
});
</script>

<style lang="scss" scoped>
.workflow-stencil {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.stencil-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .panel-title {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .search-input {
    width: 100%;
  }
}

.stencil-content {
  flex: 1;
  padding: 8px;
}

.node-category {
  margin-bottom: 8px;

  .category-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;

    &:hover {
      background: #e9ecef;
    }

    .el-icon {
      margin-right: 8px;
      transition: transform 0.2s;
      color: #606266;

      &.rotated {
        transform: rotate(90deg);
      }
    }

    .category-title {
      flex: 1;
      font-weight: 500;
      color: #303133;
    }

    .node-count {
      font-size: 12px;
      color: #909399;
    }
  }

  .category-content {
    padding: 8px 0;
  }
}

.node-template {
  margin: 8px 0;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  cursor: grab;
  transition: all 0.2s;
  user-select: none;

  &:hover {
    border-color: #1a59b0;
    box-shadow: 0 2px 8px rgba(26, 89, 176, 0.15);
    transform: translateY(-1px);
  }

  &.dragging {
    opacity: 0.6;
    transform: scale(0.95);
  }

  &:active {
    cursor: grabbing;
  }

  .node-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;

    .node-icon {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .node-info {
      flex: 1;
      min-width: 0;

      .node-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-description {
        font-size: 11px;
        color: #909399;
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .node-ports {
    border-top: 1px solid #f0f0f0;
    padding-top: 8px;

    .ports-section {
      margin-bottom: 4px;

      .port-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 11px;
        color: #606266;
        margin-bottom: 2px;

        &.input-port {
          justify-content: flex-start;
        }

        &.output-port {
          justify-content: flex-end;
        }

        .port-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #909399;
          flex-shrink: 0;
        }

        .port-label {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .stencil-header {
    padding: 12px;
  }

  .node-template {
    padding: 10px;

    .node-preview {
      .node-icon {
        width: 32px;
        height: 32px;
      }

      .node-info {
        .node-name {
          font-size: 13px;
        }

        .node-description {
          font-size: 10px;
        }
      }
    }
  }
}
</style>
