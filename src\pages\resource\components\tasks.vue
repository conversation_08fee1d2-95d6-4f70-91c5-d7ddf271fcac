<template>
  <div class="tasks-list">
    <h2>任务监控</h2>
    <el-table :data="tableData">
      <el-table-column
        v-for="item in tableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        :width="item.width || 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.value === 'startTime'">
            {{ formatDateTime(row[item.value]) }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button @click="handleEdit(row)" type="primary" size="small">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-right"
      v-model:current-page="pages.currentPage"
      v-model:page-size="pages.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      background
      size="small"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pages.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="visibleTask"
      title="任务详情"
      width="80%"
      @close="visibleTask = false"
    >
      <task-detail :taskId="currentTaskId" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { to, job_list, job_detail } from "@/api/index.js";
import { formatDateTime } from "@/utils/index.js";
import TaskDetail from "@/components/Task/TaskDetail.vue";

// Props - 接收外部传入的统计数据更新函数
const props = defineProps({
  onStatsUpdate: {
    type: Function,
    default: () => {},
  },
});

// 原始数据存储
const allTableData = ref([]);

// 分页配置
const pages = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 当前页显示的数据
const tableData = computed(() => {
  const start = (pages.currentPage - 1) * pages.pageSize;
  const end = start + pages.pageSize;
  return allTableData.value.slice(start, end);
});

// 表格列配置
const tableItems = ref([
  { label: "任务ID", value: "taskId", width: "250px" },
  { label: "任务名称", value: "jobName" },
  { label: "节点数", value: "nnodes" },
  { label: "核数", value: "ncpus" },
  { label: "起始时间", value: "startTime", width: "180px" },
  { label: "运行时间", value: "elapsed" },
  { label: "作业状态", value: "state", width: "180px" },
]);

// 任务详情弹窗
const visibleTask = ref(false);
const currentTaskId = ref(null);

// 查看任务详情
const handleEdit = (row) => {
  currentTaskId.value = row.taskId;
  visibleTask.value = true;
};

// 页码改变处理
const handleCurrentChange = (page) => {
  pages.currentPage = page;
};

// 每页条数改变处理
const handleSizeChange = (size) => {
  pages.pageSize = size;
};

// 获取数据列表
const fetchList = async () => {
  const [err, res] = await to(() => job_list());
  if (err) {
    ElMessage.error("获取数据失败");
    return;
  }

  // 存储所有数据
  allTableData.value = res.data || [];
  pages.total = allTableData.value.length;
  pages.currentPage = 1; // 重置到第一页

  // 计算统计数据并通知父组件
  const stats = {
    total: allTableData.value.length,
    completed: allTableData.value.filter((item) => item.state === "COMPLETED")
      .length,
    running: allTableData.value.filter((item) => item.state === "RUNNING")
      .length,
    failedTasks: allTableData.value.filter((item) => item.state === "FAILED")
      .length,
  };

  // 通知父组件更新统计数据
  props.onStatsUpdate(stats);
};

// 定时器变量
let timer = null;

// 启动定时器
const startTimer = () => {
  // 清除现有定时器
  if (timer) {
    clearInterval(timer);
  }
  // 设置新的定时器，每10秒执行一次
  timer = setInterval(() => {
    fetchList();
  }, 10000);
};

// 停止定时器
const stopTimer = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

// 组件挂载时获取数据并启动定时器
onMounted(() => {
  fetchList();
  startTimer();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopTimer();
});

// 暴露刷新方法和定时器控制方法给父组件
defineExpose({
  refresh: fetchList,
  startTimer,
  stopTimer,
});
</script>

<style lang="scss" scoped>
.tasks-list {
  .pagination-right {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
