<template>
  <div class="model-list">
    <div class="model-list-header">
      <h3>模型列表</h3>
      <el-input
        v-model="searchText"
        placeholder="搜索模型..."
        size="small"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="model-list-content">
      <div
        class="model-category"
        v-for="category in filteredCategories"
        :key="category.name"
      >
        <div class="category-header" @click="toggleCategory(category.name)">
          <el-icon
            class="category-icon"
            :class="{ expanded: category.expanded }"
          >
            <ArrowRight />
          </el-icon>
          <span class="category-name">{{ category.name }}</span>
          <span class="category-count">({{ category.models.length }})</span>
        </div>

        <div class="category-models" v-show="category.expanded">
          <div
            v-for="model in category.models"
            :key="model.id"
            class="model-item"
            draggable="true"
            @dragstart="handleDragStart($event, model)"
            @dragend="handleDragEnd"
          >
            <div class="model-icon">
              <el-icon><Box /></el-icon>
            </div>
            <div class="model-info">
              <div class="model-name">{{ model.name }}</div>
              <div class="model-description">{{ model.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Search, ArrowRight, Box } from "@element-plus/icons-vue";

// 定义事件
const emit = defineEmits(["drag-start"]);

// 响应式数据
const searchText = ref("");

// 模型数据
const modelCategories = ref([
  {
    name: "数据处理",
    expanded: true,
    models: [
      { id: "data-clean", name: "数据清洗", description: "清洗和预处理数据" },
      { id: "data-transform", name: "数据转换", description: "转换数据格式" },
      { id: "data-filter", name: "数据过滤", description: "过滤数据内容" },
    ],
  },
  {
    name: "机器学习",
    expanded: true,
    models: [
      {
        id: "linear-regression",
        name: "线性回归",
        description: "线性回归模型",
      },
      { id: "decision-tree", name: "决策树", description: "决策树分类模型" },
      {
        id: "neural-network",
        name: "神经网络",
        description: "深度学习神经网络",
      },
    ],
  },
  {
    name: "数据可视化",
    expanded: false,
    models: [
      { id: "chart-line", name: "折线图", description: "生成折线图" },
      { id: "chart-bar", name: "柱状图", description: "生成柱状图" },
      { id: "chart-scatter", name: "散点图", description: "生成散点图" },
    ],
  },
]);

// 计算属性：过滤后的分类
const filteredCategories = computed(() => {
  if (!searchText.value) {
    return modelCategories.value;
  }

  return modelCategories.value
    .map((category) => ({
      ...category,
      models: category.models.filter(
        (model) =>
          model.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
          model.description
            .toLowerCase()
            .includes(searchText.value.toLowerCase())
      ),
    }))
    .filter((category) => category.models.length > 0);
});

// 切换分类展开状态
const toggleCategory = (categoryName) => {
  const category = modelCategories.value.find(
    (cat) => cat.name === categoryName
  );
  if (category) {
    category.expanded = !category.expanded;
  }
};

// 处理拖拽开始
const handleDragStart = (event, model) => {
  console.log("=== 拖拽开始 ===");
  console.log("模型数据:", model);
  console.log("事件对象:", event);

  // 设置拖拽数据
  const jsonData = JSON.stringify(model);
  event.dataTransfer.setData("application/json", jsonData);
  event.dataTransfer.effectAllowed = "copy";

  console.log("设置的JSON数据:", jsonData);

  // 添加拖拽样式
  event.target.classList.add("dragging");

  // 触发父组件事件
  emit("drag-start", model);

  console.log("=== 拖拽数据设置完成 ===");
};

// 处理拖拽结束
const handleDragEnd = (event) => {
  console.log("拖拽结束");
  event.target.classList.remove("dragging");
};

onMounted(() => {
  console.log("模型列表组件已挂载");
});
</script>

<style lang="scss" scoped>
.model-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.model-list-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;

  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .search-input {
    width: 100%;
  }
}

.model-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.model-category {
  margin-bottom: 8px;

  .category-header {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f7fa;
    }

    .category-icon {
      margin-right: 8px;
      transition: transform 0.2s;

      &.expanded {
        transform: rotate(90deg);
      }
    }

    .category-name {
      flex: 1;
      font-weight: 500;
      color: #606266;
    }

    .category-count {
      font-size: 12px;
      color: #909399;
    }
  }
}

.category-models {
  padding-left: 16px;
}

.model-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 0;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  border: 1px solid transparent;

  &:hover {
    background-color: #f0f9ff;
    border-color: #409eff;
    transform: translateX(2px);
  }

  &.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  .model-icon {
    margin-right: 12px;
    color: #409eff;
    font-size: 20px;
  }

  .model-info {
    flex: 1;

    .model-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 2px;
    }

    .model-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
}

// 滚动条样式
.model-list-content::-webkit-scrollbar {
  width: 6px;
}

.model-list-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.model-list-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}
</style>
