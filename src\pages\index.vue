<template>
  <div class="container-fluid banner">
    <div class="container banner-box">
      <el-row>
        <el-col :span="18" :offset="3">
          <!-- 查询 -->
          <div class="search-box">
            <el-input
              v-model="search"
              style="max-width: 100%"
              placeholder="请输入关键词检索，默认全部数据库"
              class="input-with-select"
            >
              <template #prepend>
                <el-select v-model="searchType" style="width: 120px">
                  <el-option label="全部数据库" value="1" />
                  <el-option label="Order No." value="2" />
                  <el-option label="Tel" value="3" />
                </el-select>
              </template>
              <template #append>
                <el-button type="default">高级检索</el-button>
                <el-icon class="search-icon" :size="20">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <!-- 标签 -->
          <div class="hot-search-words">
            <span>热搜词汇：</span>
            <span
              v-for="word in hotWords"
              :key="word"
              class="hot-search-tag"
              @click="searchTags(word)"
              >{{ word }}</span
            >
          </div>
        </el-col>
        <el-col :span="8" :offset="8">
          <div class="words-box" :style="{ '--grid-columns': 3 }">
            <div
              class="words-item"
              v-for="(item, index) in dataLabels"
              :key="index"
            >
              <div class="words-label">{{ item.label }}</div>
              <div class="words-number">{{ item.number }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="words-box" :style="{ '--grid-columns': 4 }">
            <div
              class="words-item"
              v-for="(item, index) in dataLabels2"
              :key="index"
            >
              <div class="words-label">{{ item.label }}</div>
              <div class="words-number">{{ item.number }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
  <div class="container content">
    <el-row>
      <el-col :span="24">
        <div class="u-flex-x u-flex-between">
          <h3>国际数据</h3>
          <div style="color: var(--el-text-color-secondary)">检测国际数据</div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<route lang="yaml">
meta:
  layout: front
</route>

<script setup>
const searchType = ref("1");
const search = ref("");

// 热词
const hotWords = shallowRef([
  "Order No.",
  "Tel",
  "Email",
  "Address",
  "Name",
  "Company",
]);

// 数据
const dataLabels = shallowRef([
  { label: "数据库", number: "193个" },
  { label: "数据量", number: "9.05PB" },
  { label: "数据条数", number: "81.35亿" },
]);

const dataLabels2 = shallowRef([
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
  { label: "DNA与RNA", number: "54.92亿" },
]);

function searchTags(word) {
  search.value = word;
}

/**
 * 设置指定数据集的网格列数
 * @param {string} dataKey - 数据集名称 ('dataLabels' 或 'dataLabels2')
 * @param {number} columns - 列数
 */
function setGridColumns(dataKey, columns) {
  if (gridColumns.value[dataKey] !== undefined) {
    gridColumns.value[dataKey] = columns;
  }
}
</script>

<style lang="scss" scoped>
.banner {
  background-color: #0466a3;
  height: 600px;
  .banner-box {
    .search-box {
      margin-top: 100px;
    }
    .el-input {
      height: 50px;
      --el-input-border-radius: 10px;
      --el-input-border-color: #fff;

      :deep(.el-input__wrapper) {
        box-shadow: none !important;
        border: none;
        // background-color: #fff;
      }

      :deep(.el-input__wrapper:hover) {
        box-shadow: none !important;
        border: none;
      } // 修复 append 插槽中 primary 按钮样式
      :deep(.el-input-group__append) {
        background-color: #fff;
        min-width: 100px;
        display: flex;
        justify-content: space-between;
        .el-button {
          background-color: #f5f5f5;
          border-color: #f5f5f5;
          color: #000;
          height: 80%;
        }

        .el-icon {
          margin-left: 8px;
          cursor: pointer;
          color: var(--el-color-primary);
        }
      }
    }
    .el-select {
      --el-select-width: 600px;
      width: 100%;

      :deep(.el-select__wrapper) {
        height: 50px;
        background-color: #fff;
        min-width: 120px;
      }

      :deep(.el-select__wrapper:hover) {
        background-color: #fff;
      }

      :deep(.el-input) {
        width: 100%;
      }

      :deep(.el-input__inner) {
        width: 100%;
      }
    }
    .hot-search-words {
      margin-top: 15px;
      color: #fff;
      font-size: 12px;
      .hot-search-tag {
        margin: 0 15px;
        cursor: pointer;
      }
    }
    .words-box {
      margin-top: 50px;
      display: grid;
      grid-template-columns: repeat(var(--grid-columns, 3), 1fr);
      grid-gap: 20px;
      .words-item {
        text-align: center;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 10px;
        padding: 8px 10px;
        .words-label {
          font-size: 16px;
          color: #fff;
          margin-bottom: 3px;
        }
        .words-number {
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
}
.content {
}
</style>
