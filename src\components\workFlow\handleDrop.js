/**
 * 工作流节点拖拽处理工具
 * 处理从节点模板面板拖拽到画布的逻辑
 */

import { ElMessage } from "element-plus";

/**
 * 处理节点拖拽放置事件
 * @param {DragEvent} event 拖拽事件
 * @param {Graph} graph X6画布实例
 */
export const handleDrop = (event, graph) => {
  if (!graph) {
    console.warn("画布实例不存在");
    return;
  }

  event.preventDefault();

  try {
    // 获取拖拽数据
    const dragData = event.dataTransfer.getData("application/json");
    if (!dragData) {
      console.warn("未找到拖拽数据");
      return;
    }

    const data = JSON.parse(dragData);

    // 验证数据格式
    if (data.type !== "workflow-node" || !data.nodeTemplate) {
      console.warn("无效的拖拽数据格式");
      return;
    }

    // 计算放置位置
    const containerRect = event.currentTarget.getBoundingClientRect();
    const clientX = event.clientX - containerRect.left;
    const clientY = event.clientY - containerRect.top;
    const position = graph.clientToLocal(clientX, clientY);

    // 创建节点
    createNodeFromTemplate(graph, data.nodeTemplate, position);
  } catch (error) {
    console.error("处理拖拽数据失败:", error);
    ElMessage.error("创建节点失败");
  }
};

/**
 * 根据模板创建节点
 * @param {Graph} graph X6画布实例
 * @param {Object} template 节点模板
 * @param {Object} position 放置位置 {x, y}
 */
export const createNodeFromTemplate = (graph, template, position) => {
  try {
    // 确定节点形状 - 根据实际注册的节点类型
    let nodeShape = "workflow-node";

    // 根据模板类型确定节点形状
    if (template.type === "model") {
      nodeShape = "workflow-node";
    } else if (template.id === "start" || template.type === "start") {
      nodeShape = "start-node";
    } else if (template.id === "end" || template.type === "end") {
      nodeShape = "end-node";
    } else if (template.id === "decision" || template.type === "decision") {
      nodeShape = "decision-node";
    } else {
      // 默认使用工作流节点
      nodeShape = "workflow-node";
    }

    // 生成唯一的节点ID
    const nodeId = `node_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`; // 创建端口配置 - 为SVG节点添加四向连接桩
    const ports = [
      {
        id: "top",
        group: "top",
      },
      {
        id: "right",
        group: "right",
      },
      {
        id: "bottom",
        group: "bottom",
      },
      {
        id: "left",
        group: "left",
      },
    ];

    // 计算节点位置（居中放置）
    const nodeX = position.x - (template.width || 160) / 2;
    const nodeY = position.y - (template.height || 80) / 2; // 创建节点配置
    const nodeConfig = {
      id: nodeId,
      shape: nodeShape,
      x: nodeX,
      y: nodeY,
      width: template.width || 160,
      height: template.height || 80,
      label: template.name,
      ports: ports, // 添加端口配置
      data: {
        ...template,
        nodeId: nodeId,
        originalTemplate: template,
        properties: { ...template.properties },
        createdAt: new Date().toISOString(),
        version: "1.0",
        type: template.type,
        isRunning: false,
        isExpanded: false,
      },
    };

    // 添加节点到画布
    const node = graph.addNode(nodeConfig);

    console.log("创建的节点配置:", nodeConfig);
    console.log("节点getData():", node.getData());

    // 选中新创建的节点
    graph.select(node);

    // 显示成功消息
    ElMessage.success(`已添加节点: ${template.name}`);

    return node;
  } catch (error) {
    console.error("创建节点失败:", error);
    ElMessage.error("创建节点失败");
    return null;
  }
};

/**
 * 创建端口配置
 * @param {Array} ports 端口配置数组
 * @returns {Array} X6端口配置
 */
const createPortsConfig = (ports) => {
  return ports.map((port, index) => ({
    id: port.id || `port_${index}`,
    group: port.type, // 'input' 或 'output'
    args: {
      label: {
        text: port.label || "",
        position: port.type === "input" ? "left" : "right",
        style: {
          fontSize: "10px",
          fill: "#666",
        },
      },
    },
    attrs: {
      circle: {
        magnet: true,
        stroke: port.type === "input" ? "#409EFF" : "#67C23A",
        strokeWidth: 2,
        fill: "#fff",
        r: 6,
      },
    },
    // 端口数据
    data: {
      type: port.type,
      label: port.label,
      dataType: port.dataType || "any",
      required: port.required || false,
    },
  }));
};

/**
 * 创建节点样式属性
 * @param {Object} template 节点模板
 * @returns {Object} X6节点样式配置
 */
const createNodeAttrs = (template) => {
  const baseAttrs = {
    body: {
      fill: template.color || "#ffffff",
      stroke: template.color || "#1A59B0",
      strokeWidth: 2,
      rx: 8,
      ry: 8,
    },
    text: {
      fill: template.textColor || "#ffffff",
      fontSize: 12,
      fontFamily: "Microsoft YaHei, sans-serif",
      textAnchor: "middle",
      textVerticalAnchor: "middle",
      text: template.name,
    },
  };

  // 根据节点类型调整样式
  if (template.type === "input") {
    baseAttrs.body.fill = template.color || "#67C23A";
    baseAttrs.body.stroke = template.color || "#67C23A";
  } else if (template.type === "output") {
    baseAttrs.body.fill = template.color || "#E6A23C";
    baseAttrs.body.stroke = template.color || "#E6A23C";
  } else if (template.type === "processing") {
    baseAttrs.body.fill = template.color || "#409EFF";
    baseAttrs.body.stroke = template.color || "#409EFF";
  } else if (template.type === "control") {
    baseAttrs.body.fill = template.color || "#F56C6C";
    baseAttrs.body.stroke = template.color || "#F56C6C";
  }

  return baseAttrs;
};

/**
 * 验证节点模板数据
 * @param {Object} template 节点模板
 * @returns {boolean} 是否有效
 */
export const validateNodeTemplate = (template) => {
  if (!template || typeof template !== "object") {
    return false;
  }

  // 必需字段检查
  const requiredFields = ["id", "name", "type"];
  for (const field of requiredFields) {
    if (!template[field]) {
      console.warn(`节点模板缺少必需字段: ${field}`);
      return false;
    }
  }

  // 类型检查
  const validTypes = ["input", "output", "processing", "control"];
  if (!validTypes.includes(template.type)) {
    console.warn(`无效的节点类型: ${template.type}`);
    return false;
  }

  return true;
};

/**
 * 获取节点默认配置
 * @param {string} nodeType 节点类型
 * @returns {Object} 默认配置
 */
export const getNodeDefaults = (nodeType) => {
  const defaults = {
    input: {
      width: 120,
      height: 60,
      color: "#67C23A",
      textColor: "#ffffff",
    },
    output: {
      width: 120,
      height: 60,
      color: "#E6A23C",
      textColor: "#ffffff",
    },
    processing: {
      width: 140,
      height: 70,
      color: "#409EFF",
      textColor: "#ffffff",
    },
    control: {
      width: 100,
      height: 80,
      color: "#F56C6C",
      textColor: "#ffffff",
    },
  };

  return defaults[nodeType] || defaults.processing;
};

/**
 * 生成节点唯一ID
 * @param {string} prefix ID前缀
 * @returns {string} 唯一ID
 */
export const generateNodeId = (prefix = "node") => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
};

export default {
  handleDrop,
  createNodeFromTemplate,
  validateNodeTemplate,
  getNodeDefaults,
  generateNodeId,
};
