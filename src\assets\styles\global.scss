// 字体平滑处理
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: #303133;
  line-height: 1.5;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

// Bootstrap container 样式
.container {
  width: 1400px;
  margin: 0 auto;
  @media screen and (max-width: 1400px) {
    width: 100%;
  }
  @media screen and (max-width: 768px) {
    width: 100%;
  }
}

// Bootstrap container-fluid 样式
.container-fluid {
  width: 100%;
}

.u-flex-x {
  display: flex;
  flex-direction: row;
}
.u-flex-y{
  display: flex;
  flex-direction: column;
}
.u-flex-center {
  align-items: center;
}
.u-flex-between {
  justify-content: space-between;
  align-items: center;
}

