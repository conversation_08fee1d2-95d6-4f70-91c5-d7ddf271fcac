@use "sass:color";

// 1. 先覆盖变量
@use 'element-plus/theme-chalk/src/common/var.scss' as * with (
  $colors: (
    'primary': (
      'base': #1A59B0,
      'light-3': color.adjust(#1A59B0, $lightness: 10%),
      'dark-2': color.adjust(#1A59B0, $lightness: -10%)
    ),
    'success': ( 'base': #67C23A ),
    'warning': ( 'base': #FFA729 ),
    'danger': ( 'base': #EB4724 ),
    'info': ( 'base': #207CB8 )
  ),
  $text-color: (
    'primary': #000000,
    'regular': #000000,
    'secondary': #85868A,
    'placeholder': #C0C4CC
  ),
  $border-color: (
    'base': #F0F2F8,
    'light': #D9D9D9,
    'lighter': #ECF5FF
  ),
  $fill-color: (
    'light': #F7F8FA,
    'lighter': #ECF5FF
  ),
  $font-family: (
    '': "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Noto Sans SC', sans-serif"
  ),
  $font-size: (
    'extra-large': 48px,
    'large': 20px,
    'medium': 18px,
    'base': 16px,
    'small': 14px
  ),
  $header: (
    'height': 65px
  ),
  $footer: (
    'height': 80px
  ),
  $common-component-size: (
    'large': 50px,
    'default': 30px,
    'small': 30px
  ),  
  $button-padding-horizontal: (
    'large': 70px,
    'default': 20px,
    'small': 20px
  ),
  $button-padding-vertical: (
    'large': 15px,
    'default': 10px,
    'small': 10px
  ),
  $menu: (
    'item-height': 50px,
    'hover-bg-color': #ECF5FF,
    'hover-text-color': #1A59B0,
    'active-color': #1A59B0
  ),
  $table: (
    'header-bg-color': #ECF5FF,
    'header-text-color': #1A59B0,
    'row-height': 60px,
    'border-color': #F0F2F8
  ),
  $pagination: (
    'button-width': 50px,
    'button-height': 50px
  )
);

// 2. 再引入 element-plus 主题主入口，变量才会生效
// @use 'element-plus/theme-chalk/src/index.scss' as *;
