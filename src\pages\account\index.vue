<template>
  <div class="account-container">
    <div class="account-content">
      <!-- 左侧导航 -->
      <div class="account-sidebar">
        <div class="nav-menu">
          <div
            v-for="item in menuItems"
            :key="item.key"
            class="menu-item"
            :class="{ active: activeTab === item.key }"
            @click="handleTapTab(item.key)"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
      <!-- 右侧内容区域 -->
      <el-scrollbar class="account-main">
        <component :is="currentComponent" />
      </el-scrollbar>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: front
</route>

<script setup>
// 导入 Account 相关组件
import AccountInformation from "@/components/Account/AccountInformation.vue";
import AccountFiles from "@/components/Account/AccountFiles.vue";
import AccountMachine from "@/components/Account/AccountMachine.vue";
import AccountMonitor from "@/components/Account/AccountMonitor.vue";

// 当前激活的标签页
const activeTab = ref("information");

// 左侧菜单配置
const menuItems = ref([
  { key: "information", label: "用户信息", icon: "User" },
  { key: "files", label: "文件管理", icon: "Files" },
  { key: "machine", label: "机时管理", icon: "DataBoard" },
  { key: "monitor", label: "配置管理", icon: "Operation" },
]);

// http://www.aicnic.cn/jobs-solver/#/thirdLogin?app=solver&token=a9bfa954-e6cc-4425-9fd2-6b0c6e6945e8

// 注册组件映射，用于动态组件
const componentMap = {
  AccountInformation,
  AccountFiles,
  AccountMachine,
  AccountMonitor,
};

// 计算当前要显示的组件
const currentComponent = computed(() => {
  const componentName = `Account${
    activeTab.value.charAt(0).toUpperCase() + activeTab.value.slice(1)
  }`;
  return componentMap[componentName];
});

const handleTapTab = (key) => {
  if (key === "monitor") {
    window.open(
      `http://www.aicnic.cn/jobs-solver/#/thirdLogin?app=solver&token=${localStorage.getItem(
        "token"
      )}`,
      "_blank"
    );
    return;
  }
  activeTab.value = key;
};

// 组件挂载后初始化图表
onMounted(() => {});
</script>

<style lang="scss" scoped>
.account-container {
  padding: 24px;
}

.account-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  gap: 24px;
}

// 左侧边栏
.account-sidebar {
  width: 280px;
  flex-shrink: 0;

  .nav-menu {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 20px;
      font-size: 14px;
      color: #4b5563;
      cursor: pointer;
      transition: all 0.3s;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8fafc;
        color: #1a59b0;
      }

      &.active {
        background-color: #ecf5ff;
        color: #1a59b0;
        font-weight: 600;

        .el-icon {
          color: #1a59b0;
        }
      }

      .el-icon {
        font-size: 16px;
      }
    }
  }
}

// 右侧主内容区
.account-main {
  flex: 1;
  min-width: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  height: calc(100vh - 150px);
}
</style>
