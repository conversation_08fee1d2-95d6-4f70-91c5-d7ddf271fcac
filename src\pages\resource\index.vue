<template>
  <div class="resource-page container">
    <!-- 统计数据卡片 -->
    <h2>资源池详情</h2>
    <div class="stats-container">
      <div class="stats-card" @click="activeTab = 'tasks'">
        <div class="stats-content">
          <div class="stats-title">任务监控</div>
          <div class="stats-number">{{ taskStats.total }}</div>
          <div class="stats-desc">
            <span class="stats-item success"
              >完成: {{ taskStats.completed }}</span
            >
            <span class="stats-item success"
              >运行中: {{ taskStats.running }}</span
            >
            <span class="stats-item error"
              >失败: {{ taskStats.failedTasks }}</span
            >
          </div>
        </div>
      </div>
      <div class="stats-card" @click="activeTab = 'hpc'">
        <div class="stats-content">
          <div class="stats-title">HPC监控</div>
          <div class="stats-number">{{ hpcStats.total }}</div>
          <div class="stats-desc">
            <span class="stats-item success">开启: {{ hpcStats.start }}</span>
            <span class="stats-item error">关闭: {{ hpcStats.stop }}</span>
          </div>
        </div>
      </div>

      <div class="stats-card" @click="activeTab = 'queue'">
        <div class="stats-content">
          <div class="stats-title">队列监控</div>
          <div class="stats-number">{{ queueStats.total }}</div>
          <div class="stats-desc">
            <span class="stats-item success"
              >节点数: {{ queueStats.nodes }}</span
            >
            <span class="stats-item success">cpu: {{ queueStats.cpus }}</span>
            <span class="stats-item success">gpu: {{ queueStats.gpus }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务监控组件 -->
    <tasks
      v-show="activeTab === 'tasks'"
      :onStatsUpdate="handleTaskStatsUpdate"
      ref="tasksRef"
    />
    <!-- HPC监控组件 -->
    <hpcs
      v-show="activeTab === 'hpc'"
      :stats="hpcStats"
      :onStatsUpdate="handleHpcStatsUpdate"
    />
    <!-- 队列监控组件 -->
    <queues
      v-show="activeTab === 'queue'"
      :stats="queueStats"
      :onStatsUpdate="handleQueueStatsUpdate"
    />
  </div>
</template>

<route lang="yaml">
meta:
  layout: front
</route>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import Tasks from "./components/tasks.vue";
import Hpcs from "./components/hpcs.vue";
import Queues from "./components/queues.vue";

const router = useRouter();

const activeTab = ref("tasks");

// 任务统计数据
const taskStats = reactive({
  total: 0,
  completed: 0,
  running: 0,
  failedTasks: 0,
});

// HPC监控统计数据
const hpcStats = reactive({
  total: 0,
  start: 0,
  stop: 0,
});

// 队列监控统计数据
const queueStats = reactive({
  total: 0,
  nodes: 0,
  cpus: 0,
  gpus: 0,
});

// 任务组件引用
const tasksRef = ref(null);

// 处理任务统计数据更新
const handleTaskStatsUpdate = (stats) => {
  Object.assign(taskStats, stats);
};

// 处理HPC统计数据更新
const handleHpcStatsUpdate = (stats) => {
  Object.assign(hpcStats, stats);
};
// 处理队列统计数据更新
const handleQueueStatsUpdate = (stats) => {
  Object.assign(queueStats, stats);
};

onMounted(() => {
  // 组件挂载后，任务组件会自动获取数据并更新统计
});
</script>

<style lang="scss" scoped>
.resource-page {
  padding: 20px;
}

.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stats-content {
  position: relative;
  z-index: 1;
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stats-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
  line-height: 1;
}

.stats-desc {
  display: flex;
  gap: 16px;
}

.stats-item {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.05);
}

.stats-item.active {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.stats-item.idle {
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
}

.stats-item.pending {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.stats-item.running {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.stats-item.success {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.stats-item.error {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}
</style>
