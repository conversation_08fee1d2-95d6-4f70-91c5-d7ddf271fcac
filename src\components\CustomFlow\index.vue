<template>
  <div id="container">
    <div class="left-panel">
      <FlowStencil :graph="graph" @stencilReady="onStencilReady" />
    </div>
    <div class="right-panel">
      <div class="canvas-wrapper">
        <FlowCanvas
          @graphReady="onGraphReady"
          @nodeClick="onNodeClick"
          ref="canvasRef"
        />
      </div>
      <div class="toolbar">
        <el-button type="primary" size="small" @click="handleCreateWorkflow"
          >创建工作流</el-button
        >
        <!-- <el-button size="small" @click="handleRestore">还原工作流</el-button> -->
        <el-button size="small" @click="handleUndo">撤销</el-button>
        <el-button size="small" @click="handleRedo">重做</el-button>
        <!-- 可根据需要添加更多按钮 -->
      </div>
    </div>
  </div>
  <el-dialog
    title="任务配置"
    v-model="taskDialog"
    width="60%"
    :modal="false"
    draggable
  >
    <el-tabs type="border-card">
      <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label">
        <!-- 数据配置 -->
        <div v-if="item.name === 'dataFile'">
          <el-upload
            ref="uploadRef"
            v-model:file-list="taskForm.dataFile"
            :action="uploadAction"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-progress="handleUploadProgress"
            :before-upload="beforeUpload"
            :auto-upload="false"
            multiple
            drag
            class="upload-area"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖拽到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多文件上传，文件大小不超过 100MB
              </div>
            </template>
          </el-upload>
          <div class="upload-actions" v-if="taskForm.dataFile.length > 0">
            <el-button type="primary" @click="submitUpload">开始上传</el-button>
            <el-button @click="clearFiles">清空文件</el-button>
          </div>
        </div>
        <!-- 参数配置 -->
        <div v-else-if="item.name === 'parameterConfig'">
          <div style="height: 400px; margin-bottom: 10px">
            <Codemirror
              v-model="taskForm.dataContent"
              :style="{
                height: '100%',
                border: 'solid 1px #e5e6eb',
                borderRadius: '4px',
              }"
              :options="cmOptions"
              @change="updateParameterConfig"
            />
          </div>
          <div class="u-flex-x u-flex-center">
            <div style="width: 100px">配置文件名</div>
            <el-input
              v-model="taskForm.dataName"
              placeholder="请输入配置文件名称"
            />
          </div>
          <div style="margin-top: 20px; text-align: right">
            <el-button
              type="primary"
              size="small"
              @click="validateParameterConfig"
              >校验</el-button
            >
          </div>
        </div>

        <!-- 资源配置 -->
        <div v-else-if="item.name === 'resourceConfig'">
          <div class="resource-item">
            <div class="resource-item_label">
              <span>节点数</span>
            </div>
            <div class="resource-item_content">
              <el-select
                v-model="taskForm.numberOfNodes"
                placeholder="请输入资源配置"
              >
                <el-option
                  v-for="item in ['1', '2', '4', '8', '16']"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="resource-item">
            <div class="resource-item_label">
              <span>核心数</span>
            </div>
            <div class="resource-item_content">
              <el-select
                v-model="taskForm.coresPerNode"
                placeholder="请输入资源配置"
              >
                <el-option
                  v-for="item in ['1', '2', '4', '8', '16']"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button size="small" @click="closeDialog">取消</el-button>
      <el-button size="small" type="primary" @click="closeDialog"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, h } from "vue";
import { useRouter } from "vue-router";
import FlowStencil from "./FlowStencil.vue";
import FlowCanvas from "./FlowCanvas.vue";
import {
  ElButton,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElSelect,
  ElOption,
  ElLoading,
} from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
// 导入 vue-codemirror
import { Codemirror } from "vue-codemirror";
// 导入 codemirror 基本设置和扩展
import { basicSetup } from "codemirror";
import { EditorView } from "@codemirror/view";
import { javascript } from "@codemirror/lang-javascript";
import { json } from "@codemirror/lang-json";

import { task_create, file_upload, to } from "@/api/index.js";

// 初始化 router
const router = useRouter();

// 环境变量配置
const env = import.meta.env.VITE_API_BASE_ENV || "dev";

// 文件上传配置
const uploadAction = computed(
  () =>
    `/jobs-solver/api/${env}/files/chunk/upload2?token=${localStorage.getItem(
      "token"
    )}`
);
const uploadHeaders = computed(() => {
  const token = localStorage.getItem("token");
  return {
    Authorization: token ? `${token}` : "",
  };
});

// CodeMirror 编辑器配置
const cmOptions = {
  // 基础配置
  tabSize: 2,
  lineNumbers: true,
  line: true,
  indentWithTabs: false,
  smartIndent: true,
  // 外观配置
  theme: "light",
  // 编辑器扩展
  extensions: [
    basicSetup,
    javascript(),
    json(),
    EditorView.lineWrapping,
    EditorView.theme({
      "&": {
        fontSize: "14px",
        height: "100%",
      },
      ".cm-scroller": {
        overflow: "auto",
        fontFamily: "Consolas, Monaco, 'Courier New', monospace",
      },
    }),
  ],
};

// dialog 配置
const tabs = [
  { label: "数据选择", name: "dataFile" },
  { label: "参数配置", name: "parameterConfig" },
  { label: "资源配置", name: "resourceConfig" },
];

const taskForm = reactive({
  taskName: "",
  taskDescription: "",
  workflowType: "数据处理",
  priority: "中",
  expectedDuration: "",
  notifyEmail: "",
  tags: "",
  dataFile: [],
  dataName: "G89-1",
  numberOfNodes: "1",
  coresPerNode: "1",
  dataContent: `RUNSPEC

TITLE
  'G89-1'

MODEL
ISOTHERMAL

DIMENS
  100 100 50 /

START
  1 'JAN' 2020 /

METRIC
NINEPOINT
GAS
OIL
WATER

FULLIMP
NOMIX

COMPS
  9 /

MONITOR
RSSPEC
NOINSPEC

-- EQLDIMS
--   1 100 20 1 20 /

-- REGDIMS
--   16 3 /

TABDIMS
1 1 1 /

WELLDIMS
  100 1* 4 30 30 /

MEMORY
  1000 /

--PARALLEL
-- 8 /

UNIFOUT
UNIFIN
NOECHO

--GRID

--GRIDFILE
--  0 1 /

--INCLUDE
--  'G89-1.GRDECL' /

METHOD
FIM petsc

SSMSTA
#Successive substitution in Stablility Analysis
#maxit tolerance
100	1e-3 1e-4

NRSTA
#Newton Raphson in Stablility Analysis
#maxit tolerance
55 1e-3

SSMSP
#Successive substitution in Phase Split
#maxit tolerance
4	1E-1
/

NRSP
#Newton Raphson in Phase Split
#maxit tolerance
55  1e-3
/

RR
#Rachford Rice equation parameters
#maxit tolerance
30	1e-3
/

EQUALS
  TOPS 2800 1 100 1 100 1 1 /
  DX   20   1 100 1 100 1 50 /
  DY   20   1 100 1 100 1 50 /
  DZ   0.3  1 100 1 100 1 50 /
  PERMX 5   1 100 1 100 1 50 /
  PORO 0.16 1 100 1 100 1 50 /
  NTG 1.0   1 100 1 100 1 50 /
/


COPY
  PERMX PERMY 1 100 1 100 1 50 /
  PERMX PERMZ 1 100 1 100 1 50 /
/

MULTIPLY
  PERMZ 0.5 1 100 1 100 1 50 /
/

PROPS

INCLUDE
  'G89-1.PVO' /

ECHO

ROCK
LINEAR01 392 0.000012
/

PVTW
  1.01325 1 4.79e-05 0.4 0
/

ZMFVD
  0  	0.004530309408  0.2470268713  0.0832156834  0.1498602351  0.1274787065  0.1124476799  0.1208682549  0.08533582822  0.06923643134
  5000  0.004530309408  0.2470268713  0.0832156834  0.1498602351  0.1274787065  0.1124476799  0.1208682549  0.08533582822  0.06923643134
/

SWOF
--
--  Water/Oil  Saturation  Functions
--
0.375	  0	          0.8	    0
0.3917	0.002459675	0.6859	0
0.4084	0.006957011	0.5832	0
0.4251	0.012780845	0.4913	0
0.4418	0.019677398	0.4096	0
0.4585	0.0275	    0.3375	0
0.4752	0.036149689	0.2744	0
0.4919	0.045553814	0.2197	0
0.5086	0.055656087	0.1728	0
0.5253	0.066411219	0.1331	0
0.542	   0.077781746	0.1	0
0.5587	0.089736002	0.0729	0
0.5754	0.10224676	0.0512	0
0.5921	0.115290286	0.0343	0
0.6088	0.128845644	0.0216	0
0.6255	0.142894192	0.0125	0
0.6422	0.157419186	0.0064	0
0.6589	0.172405481	0.0027	0
0.6756	0.187839293	0.0008	0
0.6923	0.203708002	0.0001	0
0.709	0.22	0	0
/

SGOF
0	0	0.8	0
0.1117	0.018528361	0.6859	0
0.1234	0.039716412	0.5832	0
0.1351	0.0620398	0.4913	0
0.1468	0.085133992	0.4096	0
0.1585	0.10881882	0.3375	0
0.1702	0.132985223	0.2744	0
0.1819	0.157559594	0.2197	0
0.1936	0.182488707	0.1728	0
0.2053	0.207732176	0.1331	0
0.217	0.233258248	0.1	0
0.2287	0.259041271	0.0729	0
0.2404	0.285060065	0.0512	0
0.2521	0.311296829	0.0343	0
0.2638	0.337736383	0.0216	0
0.2755	0.364365622	0.0125	0
0.2872	0.391173107	0.0064	0
0.2989	0.418148769	0.0027	0
0.3106	0.445283666	0.0008	0
0.3223	0.472569806	0.0001	0
0.334	0.5	0	0
/

REGIONS



SOLUTION

ECHO

EQUIL
  2800 392 6000 1* 0.1 1* 1* 1* 1* 1* 1*
/

ECHO

RPTSOL
  'RESTART=2' 'FIP=3' 'FIPRESV'
/

RPTRST
  'BASIC=2' 'SWAT' 'SOIL' 'SGAS' 'PRESSURE' 'PCOG' 'RS' 'VOIL' 'FIP' 'STEN' 'TOTCOMP' 'XMF' 'YMF'
/

OUTSOL
  'PRES' 'SOIL' 'SGAS' 'SWAT' 'XMF' 'YMF' 'ZMF'
/

SUMMARY

TCPU
ELAPSED
RUNSUM
FOPR
FGPR
FGIR
FGOR
FWPR
FLPR
FPR
FWCT
WBP
/
WBHP
/
FOPT
FGPT
FGIT
FWPT
FLPT

WOPR
/
WOPT
/
WOPRH
/
WOPTH
/
WGOR
/
WGORH
/

WLPR
/
WLPT
/
WLPRH
/
WLPTH
/
WWPR
/
WWPT
/
WGPR
/
WGPT
/
WGPRH
/
WGPTH
/
WWIR
  /
WEIT
  /
WGIR
  /
WGIT
  /
WGIRH
  /
WGITH
  /
WGVIR
/
WGVIT
/

WWCT
/
WWCTH
/
WGOR
/
WBP
/
WBP9
/
WBHP
/
WPI
/

RPTSCHED
PRE
PGAS
PWAT
/

VTKSCHED
PRESSURE
SGAS SOIL
/

SCHEDULE

--RPTSCHED
--  'RESTART=2' 'FIP=3' 'WELLS=5' 'SUMMARY=1' /

TUNING
-- Init     max    min   incre   chop    cut
   1       31    0.001      5    0.3    0.3                    /
--  dPlim  dSlim   dNlim   dVerrlim
     300     1    0.3    0.001                               /
-- itNRmax  NRtol  dPmax  dSmax  dPmin   dSmin   dVerrmax
       20    1E-2   200    0.2    10    1E-2    0.01          /
/

--TUNING
--  1.0 31.0 0.001 /
--                 /
--                 /

--TSTEP
--3
--/

INCLUDE
  'G89-1-50W-V2.SCH' /

END
`,
  numberOfNodes: "1",
  coresPerNode: "1",
});

// 文件上传相关
const uploadRef = ref(null);

// 上传前校验
const beforeUpload = (file) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.error("文件大小不能超过 100MB!");
    return false;
  }
  return true;
};

// 上传成功回调
const handleUploadSuccess = (response, file, fileList) => {
  ElMessage.success(`文件 ${file.name} 上传成功!`);
  console.log("上传成功:", response);
};

// 上传失败回调
const handleUploadError = (error, file, fileList) => {
  ElMessage.error(`文件 ${file.name} 上传失败!`);
  console.error("上传失败:", error);
};

// 上传进度回调
const handleUploadProgress = (event, file, fileList) => {
  console.log("上传进度:", Math.round(event.percent));
};

// 手动提交上传
const submitUpload = () => {
  if (uploadRef.value) {
    // Element Plus el-upload 组件的正确方法
    try {
      // 方法1: 使用 submit 方法
      if (typeof uploadRef.value.submit === "function") {
        uploadRef.value.submit();
        return;
      }

      // 方法2: 使用 $refs 访问原生方法
      if (uploadRef.value.$refs && uploadRef.value.$refs.upload) {
        uploadRef.value.$refs.upload.submit();
        return;
      }

      // 方法3: 手动触发上传
      const fileList = taskForm.dataFile;
      if (fileList && fileList.length > 0) {
        // 调用文件上传 API
        fileList.forEach(async (file) => {
          if (file.raw && file.status === "ready") {
            const formData = new FormData();
            formData.append("chunkNumber", file.chunkNumber || 1);
            formData.append("chunkSize", file.chunkSize || file.raw.size);
            formData.append("currentChunkSize", file.raw.size);
            formData.append("totalSize", file.raw.size);
            formData.append("identifier", file.uid);
            formData.append("filename", file.name);
            formData.append("relativePath", file.name || "");
            formData.append("totalChunks", 1); // 假设单个文件不分块
            formData.append("files", file.raw);

            try {
              const [err, res] = await to(() => file_upload(formData));
              if (err) {
                ElMessage.error(`文件 ${file.name} 上传失败`);
                console.error("上传失败:", err);
              } else {
                ElMessage.success(`文件 ${file.name} 上传成功`);
                file.status = "success";
              }
            } catch (error) {
              ElMessage.error(`文件 ${file.name} 上传出错`);
              console.error("上传出错:", error);
            }
          }
        });
      } else {
        ElMessage.warning("请先选择要上传的文件");
      }
    } catch (error) {
      ElMessage.error("上传失败: " + error.message);
      console.error("Upload error:", error);
    }
  } else {
    ElMessage.warning("上传组件未就绪");
  }
};

// 清空文件列表
const clearFiles = () => {
  try {
    if (uploadRef.value) {
      // Element Plus el-upload 清空文件列表的正确方法
      if (typeof uploadRef.value.clearFiles === "function") {
        uploadRef.value.clearFiles();
      } else if (uploadRef.value.$refs && uploadRef.value.$refs.upload) {
        uploadRef.value.$refs.upload.clearFiles();
      }
    }
    // 清空表单中的文件列表
    taskForm.dataFile = [];
    ElMessage.success("文件列表已清空");
  } catch (error) {
    console.error("清空文件列表失败:", error);
    // 即使组件方法失败，也要清空表单数据
    taskForm.dataFile = [];
  }
};

// 获取文件列表
const fileList = ref([]);

//
function fileChange(file, fileList) {
  // 处理文件变化逻辑
  console.log("文件变化:", file, fileList);
  // 可以在这里添加文件变化时的处理逻辑
}

const taskDialog = ref(false);
function openDialog() {
  taskDialog.value = true;
}
function closeDialog() {
  // 关闭对话框时可以添加一些额外的处理逻辑
  taskDialog.value = false;
}

const graph = ref(null);
const stencil = ref(null);
const canvasRef = ref(null);

function onNodeClick(node) {
  console.log("Clicked node:", node);
  openDialog();
}

// 画布初始化完成回调
function onGraphReady(g) {
  graph.value = g;
  // 这里可以做 graph 相关的后续操作
}
// stencil 初始化完成回调
function onStencilReady(s) {
  stencil.value = s;
}

function getExecutionOrder(cells) {
  // 1. 分离节点和边
  const nodes = cells.filter((cell) => cell.shape !== "edge");
  const edges = cells.filter((cell) => cell.shape === "edge");

  // 2. 统计每个节点的入度
  const inDegree = {};
  nodes.forEach((node) => (inDegree[node.id] = 0));
  edges.forEach((edge) => {
    if (edge.target && edge.target.cell) {
      inDegree[edge.target.cell]++;
    }
  });

  // 3. 找到入度为0的起点
  const startNodes = nodes.filter((node) => inDegree[node.id] === 0);

  // 4. 按边遍历（BFS/DFS均可），这里用BFS
  const order = [];
  const visited = new Set();
  const queue = [...startNodes];

  while (queue.length) {
    const node = queue.shift();
    if (visited.has(node.id)) continue;
    order.push(node);
    visited.add(node.id);
    // 找到以当前节点为source的所有边，推入target节点
    edges.forEach((edge) => {
      if (edge.source && edge.source.cell === node.id) {
        const nextNode = nodes.find((n) => n.id === edge.target.cell);
        if (nextNode && !visited.has(nextNode.id)) {
          queue.push(nextNode);
        }
      }
    });
  }
  return order; // 这里返回的是节点对象顺序
}

async function handleCreateWorkflow() {
  if (!graph.value) {
    ElMessage.warning("请先初始化图形");
    return;
  }

  try {
    // 使用自定义message来实现水平布局
    const { value } = await ElMessageBox({
      title: "创建工作流",
      message: h(
        "div",
        { style: "display: flex; align-items: center; gap: 12px;" },
        [h("span", { style: "flex-shrink: 0; min-width: 80px;" }, "任务名称：")]
      ),
      showInput: true,
      inputPattern: /\S+/,
      inputErrorMessage: "任务名称不能为空",
      inputPlaceholder: "请输入任务名称",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      beforeClose: (action, instance, done) => {
        if (action === "confirm") {
          if (!instance.inputValue || !instance.inputValue.trim()) {
            ElMessage.error("任务名称不能为空");
            return;
          }
        }
        done();
      },
    });

    taskForm.taskName = value;
    await createWorkflowTask();
  } catch (error) {
    // 用户取消或关闭弹窗
    console.log("用户取消创建工作流", error);
  }
}

// 创建工作流任务
async function createWorkflowTask() {
  const loading = ElLoading.service({
    lock: true,
    text: "正在创建工作流...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const params = {
      numberOfNodes: Number(taskForm.numberOfNodes),
      coresPerNode: Number(taskForm.coresPerNode),
      filesPaths: taskForm.dataFile.map((file) => file.name).join(";"),
      dataName: `${taskForm.dataName}.DATA`,
      dataContent: taskForm.dataContent,
      taskName: taskForm.taskName,
    };

    const [err, res] = await to(() => task_create(params));
    if (err) {
      ElMessage.error("创建工作流失败: " + err.message);
      return;
    }

    const data = graph.value.toJSON();
    console.log("创建工作流数据", getExecutionOrder(data.cells));
    ElMessage.success("工作流创建成功！");
    // 重置表单
    taskForm.taskName = "";
    router.push(`/resource`);
  } finally {
    loading.close();
  }
}

function handleRestore() {
  if (graph.value) {
    graph.value.restore();
  } else {
    ElMessage.warning("图形未初始化");
  }
}

function handleUndo() {
  if (graph.value && graph.value.canUndo()) graph.value.undo();
}
function handleRedo() {
  if (graph.value && graph.value.canRedo()) graph.value.redo();
}

function updateParameterConfig(value, cm) {
  console.log(value, cm);
}

function validateParameterConfig() {
  // 校验逻辑
  try {
    // 这里可以添加真实的校验逻辑
    // 例如检查参数格式、必填项等
    ElMessage.success("参数配置校验成功！");
  } catch (error) {
    ElMessage.error("参数配置校验失败: " + error.message);
  }
}
</script>

<style scoped lang="scss">
#container {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  display: flex;
  height: calc(100vh - 60px);
  border: 1px solid #dfe3e8;
}
.left-panel {
  width: 300px;
  height: 100%;
  border-right: 1px solid #dfe3e8;
  background: #fff;
  display: flex;
  flex-direction: column;
}
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.canvas-wrapper {
  flex: 1;
  min-height: 0;
  background: #f8f9fb;
  position: relative;
}
.toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 10px 20px;
  border-top: 1px solid #e5e6eb;
  background: #fff;
  gap: 12px;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.03);
}

.resource-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .resource-item_label {
    flex: 0 0 100px;
    font-weight: bold;
  }

  .resource-item_content {
    flex: 1;
  }
  .resource-item_content .el-input {
    width: 100%;
  }
}

// 文件上传样式
.upload-area {
  margin-bottom: 20px;

  :deep(.el-upload) {
    width: 100%;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 160px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: #409eff;
    }
  }

  :deep(.el-icon--upload) {
    font-size: 28px;
    color: #8c939d;
    margin: 40px 0 16px;
    line-height: 50px;
  }

  :deep(.el-upload__text) {
    color: #606266;
    font-size: 14px;
    text-align: center;

    em {
      color: #409eff;
      font-style: normal;
    }
  }

  :deep(.el-upload__tip) {
    font-size: 12px;
    color: #999;
    margin-top: 7px;
  }
}

.upload-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}
</style>

<!-- 添加全局样式来支持MessageBox的水平布局 -->
<style lang="scss">
.el-message-box {
  .el-message-box__content {
    .el-message-box__input {
      margin-left: 0;

      .el-input {
        width: 100%;
      }
    }
  }
}
</style>
