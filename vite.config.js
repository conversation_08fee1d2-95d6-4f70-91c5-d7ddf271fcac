// Plugins
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import Fonts from "unplugin-fonts/vite";
import Layouts from "vite-plugin-vue-layouts-next";
import Vue from "@vitejs/plugin-vue";
import VueRouter from "unplugin-vue-router/vite";
import { VueRouterAutoImports } from "unplugin-vue-router";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";

// Utilities
import { defineConfig } from "vite";
import { fileURLToPath, URL } from "node:url";
import { resolve } from "path";

// https://vite.dev/config/
export default defineConfig({
  transpileDependencies: ["@kitware/vtk.js"],
  plugins: [
    VueRouter(),
    Layouts(),
    Vue(),
    Components({
      resolvers: [
        ElementPlusResolver({ importStyle: "sass" }),
        IconsResolver({
          enabledCollections: ["ep"],
        }),
      ],
      // 添加对 vue-codemirror 的支持
      include: [/\.vue$/, /\.vue\?vue/],
    }),
    Icons({
      autoInstall: true,
    }),
    Fonts({
      google: {
        families: [
          {
            name: "Roboto",
            styles: "wght@100;300;400;500;700;900",
          },
        ],
      },
    }),
    AutoImport({
      imports: [
        "vue",
        VueRouterAutoImports,
        {
          pinia: ["defineStore", "storeToRefs"],
        },
      ],
      eslintrc: {
        enabled: true,
      },
      vueTemplate: true,
      resolvers: [ElementPlusResolver({ importStyle: "sass" })],
    }),
  ],
  optimizeDeps: {
    exclude: [
      "vue-router",
      "unplugin-vue-router/runtime",
      "unplugin-vue-router/data-loaders",
      "unplugin-vue-router/data-loaders/basic",
    ],
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
    extensions: [".js", ".json", ".jsx", ".mjs", ".ts", ".tsx", ".vue"],
  },
  server: {
    port: 3000,
    host: "0.0.0.0",
    // open: true,
    proxy: {
      "/jobs-solver": {
        target: "http://www.aicnic.cn/jobs-solver/",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/jobs-solver/, "/"),
      },
      "/solver": {
        target: "http://www.aicnic.cn/solver/",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/solver/, "/"),
      },
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE",
    },
  },
  css: {
    preprocessorOptions: {
      sass: {
        api: "modern-compiler",
      },
      scss: {
        api: "modern-compiler",
      },
    },
  },
  base: "/oilgas/",
  build: {
    outDir: "./dist",
    assetsDir: "static",
    rollupOptions: {
      input: {
        main: resolve(__dirname, "index.html"),
      },
      output: {
        assetFileNames: "static/[name][extname]",
      },
    },
  },
  publicDir: "static",
});
