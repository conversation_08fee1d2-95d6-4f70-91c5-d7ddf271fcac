<template>
  <div class="model-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <h1>模型库</h1>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索模型、算法、预训练模型等"
            size="large"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container" style="margin-top: 20px">
      <el-row :gutter="24">
        <!-- 左侧筛选面板 -->
        <el-col :span="6">
          <div class="filter-panel">
            <!-- 快捷筛选 -->
            <div class="filter-section">
              <h3>快捷筛选</h3>
              <div class="quick-filters">
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'hot' }"
                  @click="setQuickFilter('hot')"
                >
                  热门
                </div>
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'new' }"
                  @click="setQuickFilter('new')"
                >
                  最新
                </div>
                <div
                  class="filter-tag"
                  :class="{ active: quickFilter === 'free' }"
                  @click="setQuickFilter('free')"
                >
                  免费
                </div>
              </div>
            </div>
            <!-- 模型框架 -->
            <div class="filter-section">
              <h3>模型框架</h3>
              <el-checkbox-group
                v-model="filters.frameworks"
                class="filter-list"
              >
                <el-checkbox label="PyTorch">PyTorch</el-checkbox>
                <el-checkbox label="TensorFlow">TensorFlow</el-checkbox>
                <el-checkbox label="ONNX">ONNX</el-checkbox>
                <el-checkbox label="Hugging Face">Hugging Face</el-checkbox>
                <el-checkbox label="PaddlePaddle">PaddlePaddle</el-checkbox>
                <el-checkbox label="MindSpore">MindSpore</el-checkbox>
                <el-checkbox label="其他">其他</el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- 应用领域 -->
            <div class="filter-section">
              <h3>应用领域</h3>
              <el-checkbox-group v-model="filters.domains" class="filter-list">
                <el-checkbox label="计算机视觉">计算机视觉</el-checkbox>
                <el-checkbox label="自然语言处理">自然语言处理</el-checkbox>
                <el-checkbox label="语音识别">语音识别</el-checkbox>
                <el-checkbox label="推荐系统">推荐系统</el-checkbox>
                <el-checkbox label="强化学习">强化学习</el-checkbox>
                <el-checkbox label="多模态">多模态</el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- 模型规模 -->
            <div class="filter-section">
              <h3>模型规模</h3>
              <el-checkbox-group
                v-model="filters.modelSize"
                class="filter-list"
              >
                <el-checkbox label="< 10M">< 10M</el-checkbox>
                <el-checkbox label="10M - 100M">10M - 100M</el-checkbox>
                <el-checkbox label="100M - 1B">100M - 1B</el-checkbox>
                <el-checkbox label="1B - 10B">1B - 10B</el-checkbox>
                <el-checkbox label="> 10B">> 10B</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 开源协议 -->
            <div class="filter-section">
              <h3>开源协议</h3>
              <el-checkbox-group v-model="filters.licenses" class="filter-list">
                <el-checkbox label="MIT">MIT</el-checkbox>
                <el-checkbox label="Apache-2.0">Apache-2.0</el-checkbox>
                <el-checkbox label="GPL-3.0">GPL-3.0</el-checkbox>
                <el-checkbox label="CC BY-4.0">CC BY-4.0</el-checkbox>
                <el-checkbox label="自定义">自定义</el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 重置按钮 -->
            <div class="filter-actions">
              <el-button @click="resetFilters" text>
                <el-icon><RefreshRight /></el-icon>
                重置筛选
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 右侧内容区域 -->
        <el-col :span="18">
          <div class="content-area">
            <!-- 排序和视图切换 -->
            <div class="content-header">
              <div class="result-info">
                共 <span class="highlight">{{ total }}</span> 个模型
              </div>
              <div class="header-actions">
                <div class="sort-select">
                  <span>排序：</span>
                  <el-select v-model="sortBy" size="small">
                    <el-option label="相关性" value="relevance" />
                    <el-option label="最新" value="newest" />
                    <el-option label="精度" value="accuracy" />
                    <el-option label="模型大小" value="size" />
                  </el-select>
                </div>
              </div>
            </div>
            <!-- 模型列表 -->
            <div class="dataset-list" v-loading="loading">
              <div
                v-for="model in modelList"
                :key="model.id"
                class="dataset-card"
                @click="goToDetail(model.id)"
              >
                <div class="card-main">
                  <div class="card-left">
                    <div class="dataset-image">
                      <el-image
                        :src="model.image"
                        :alt="model.title"
                        width="100%"
                        height="100%"
                        fit="cover"
                      ></el-image>
                    </div>
                  </div>
                  <div class="card-content">
                    <div class="dataset-header">
                      <h3 class="dataset-title">{{ model.title }}</h3>
                      <div class="dataset-tags">
                        <el-tag size="small" type="primary">{{
                          model.framework
                        }}</el-tag>
                        <el-tag size="small">{{ model.domain }}</el-tag>
                        <el-tag size="small" v-if="model.isFree" type="success"
                          >开源</el-tag
                        >
                      </div>
                    </div>
                    <p class="dataset-description">{{ model.description }}</p>
                  </div>
                </div>
                <div class="dataset-meta">
                  <div class="meta-item">
                    <el-icon><User /></el-icon>
                    <span>{{ model.author }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Calendar /></el-icon>
                    <span>{{ formatDate(model.updateTime) }}</span>
                  </div>
                  <div class="meta-item">
                    <el-icon><Download /></el-icon>
                    <span>{{ formatNumber(model.downloads) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-area">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[12, 24, 48]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<route lang="yaml">
meta:
  layout: front
</route>
<script setup>
import dayjs from "dayjs";

// 响应式数据
const loading = ref(false);
const searchKeyword = ref("");
const sortBy = ref("downloads");
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const quickFilter = ref("");

// 筛选条件
const filters = reactive({
  frameworks: [],
  domains: [],
  modelSize: [],
  licenses: [],
});

// 模型列表
const modelList = ref([]);

// 模拟数据
const mockModels = [
  {
    id: 1,
    title: "ResNet-50 图像分类模型",
    description:
      "基于残差网络的深度学习模型，在ImageNet数据集上预训练，适用于图像分类任务，具有优秀的准确率和推理速度。",
    framework: "PyTorch",
    domain: "计算机视觉",
    author: "Meta AI",
    updateTime: new Date("2024-01-15"),
    downloads: 125000,
    accuracy: 76.2,
    modelSize: "25.6M",
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 2,
    title: "BERT-Base 中文预训练模型",
    description:
      "Google开发的双向编码器表示模型，在大规模中文语料上预训练，适用于文本分类、命名实体识别等NLP任务。",
    framework: "TensorFlow",
    domain: "自然语言处理",
    author: "Google AI",
    updateTime: new Date("2024-02-20"),
    downloads: 89000,
    accuracy: 88.5,
    modelSize: "110M",
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 3,
    title: "YOLO-v8 目标检测模型",
    description:
      "最新版本的YOLO目标检测模型，在速度和精度之间取得了优秀的平衡，支持实时目标检测和实例分割。",
    framework: "PyTorch",
    domain: "计算机视觉",
    author: "Ultralytics",
    updateTime: new Date("2024-03-10"),
    downloads: 67000,
    accuracy: 89.1,
    modelSize: "43.7M",
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 4,
    title: "Whisper 语音识别模型",
    description:
      "OpenAI开发的多语言语音识别模型，支持99种语言的语音转文字，具有强大的语音识别和翻译能力。",
    framework: "PyTorch",
    domain: "语音识别",
    author: "OpenAI",
    updateTime: new Date("2024-03-25"),
    downloads: 45000,
    accuracy: 92.3,
    modelSize: "244M",
    isFree: true,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
];

// 工具方法
const formatDate = (date) => {
  return dayjs(date).format("YYYY-MM-DD");
};

const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// 事件处理方法
const router = useRouter();
const goToDetail = (modelId) => {
  router.push(`/model/detail/${modelId}`);
};

const handleSearch = () => {
  console.log("搜索关键词:", searchKeyword.value);
  loadModels();
};

const setQuickFilter = (filter) => {
  quickFilter.value = quickFilter.value === filter ? "" : filter;
  loadModels();
};

const resetFilters = () => {
  filters.frameworks = [];
  filters.domains = [];
  filters.modelSize = [];
  filters.licenses = [];
  quickFilter.value = "";
  loadModels();
};

const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  loadModels();
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
  loadModels();
};

const loadModels = async () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    modelList.value = mockModels;
    total.value = mockModels.length;
    loading.value = false;
  }, 500);
};

// 初始化
onMounted(() => {
  loadModels();
});
</script>

<style lang="scss" scoped>
.model-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

// 顶部导航
.top-nav {
  background: white;
  border-bottom: 1px solid #e5e6eb;
  position: sticky;
  top: 0;
  z-index: 100;

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nav-left {
      .logo {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;

        img {
          width: 32px;
          height: 32px;
        }
      }
    }

    .nav-right {
      display: flex;
      gap: 12px;
    }
  }
}

// 搜索区域
.search-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 16px;
  color: white;

  .search-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 32px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .search-box {
      margin-bottom: 32px;

      .search-input {
        max-width: 600px;

        :deep(.el-input__wrapper) {
          border-radius: 50px;
          padding: 12px 20px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        :deep(.el-input-group__append) {
          border-radius: 0 50px 50px 0;
          background: #409eff;
          border-color: #409eff;

          .el-button {
            border: none;
            color: white;
          }
        }
      }
    }
  }
}

// 筛选面板
.filter-panel {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .filter-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f3f4f6;
    }

    .quick-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .filter-tag {
        padding: 6px 12px;
        border: 1px solid #d1d5db;
        border-radius: 16px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #3b82f6;
          color: #3b82f6;
        }

        &.active {
          background: #3b82f6;
          border-color: #3b82f6;
          color: white;
        }
      }
    }

    .filter-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      :deep(.el-checkbox) {
        margin: 0;

        .el-checkbox__label {
          font-size: 14px;
          color: #4b5563;
        }
      }
    }
  }

  .filter-actions {
    padding-top: 16px;
    border-top: 1px solid #f3f4f6;
  }
}

// 内容区域
.content-area {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f3f4f6;

    .result-info {
      font-size: 16px;
      color: #6b7280;

      .highlight {
        color: #3b82f6;
        font-weight: 600;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      span {
        width: 80px;
      }
      .sort-select {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #6b7280;
        width: 120px;
      }
    }
  }
  .dataset-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    .dataset-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      padding: 20px;
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      gap: 16px;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border-color: #3b82f6;
        cursor: pointer;
      }

      .card-main {
        display: flex;
        gap: 20px;

        .card-left {
          flex-shrink: 0;

          .dataset-image {
            width: 160px;
            height: 120px;
            border-radius: 8px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .card-content {
          flex: 1;
          min-width: 0; // 防止flex子元素溢出

          .dataset-header {
            margin-bottom: 12px;

            .dataset-title {
              font-size: 18px;
              font-weight: 600;
              color: #1f2937;
              margin: 0 0 8px 0;
              line-height: 1.3;
              // 标题最多一行
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .dataset-tags {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;
            }
          }

          .dataset-description {
            color: #6b7280;
            line-height: 1.5;
            margin: 0;
            font-size: 14px;
            // 描述最多两行
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-clamp: 2;
            overflow: hidden;
          }
        }
      }

      .dataset-meta {
        display: flex;
        align-items: center;
        gap: 20px;
        padding-top: 12px;
        border-top: 1px solid #f3f4f6;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #6b7280;

          .el-icon {
            font-size: 14px;
          }
        }
      }

      // 网格模式
      &.grid-mode {
        .grid-card {
          .card-image {
            width: 100%;
            height: 150px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .card-body {
            .card-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin: 0 0 8px 0;
            }
            .card-desc {
              color: #6b7280;
              font-size: 14px;
              line-height: 1.5;
              margin-bottom: 12px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              line-clamp: 2;
              overflow: hidden;
            }

            .card-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .card-meta {
                font-size: 12px;
                color: #9ca3af;

                span {
                  display: block;
                }
              }
            }
          }
        }
      }
    }

    // 网格布局
    &:has(.grid-mode) {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }
  }

  .pagination-area {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #f3f4f6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-section {
    padding: 40px 16px;

    .search-container {
      h1 {
        font-size: 32px;
      }

      .search-tabs {
        gap: 16px;
      }
    }
  }

  .main-container {
    margin: -20px 16px 0;

    .el-col:first-child {
      margin-bottom: 20px;
    }
  }
  .content-area {
    .content-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .dataset-list {
      grid-template-columns: 1fr;

      .dataset-card {
        .card-main {
          flex-direction: column;

          .card-left {
            align-self: center;
          }
        }

        .dataset-meta {
          justify-content: center;
          flex-wrap: wrap;
          gap: 12px;
        }
      }
    }
  }
}
</style>
