{"name": "oilgas", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.8", "@element-plus/icons-vue": "^2.3.1", "@kitware/vtk.js": "^33.1.1", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.10", "html2canvas": "^1.4.1", "insert-css": "^2.0.0", "marked": "^15.0.12", "pinia-plugin-persistedstate": "^4.3.0", "three": "^0.176.0", "three-stdlib": "^2.36.0", "vue": "^3.5.13", "vue-codemirror": "^6.1.1", "vue-web-terminal": "^3.4.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "pinia": "^3.0.2", "sass-embedded": "^1.89.0", "unplugin-auto-import": "^19.2.0", "unplugin-fonts": "^1.3.1", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.5.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-plugin-vue-layouts-next": "^0.1.2", "vue-router": "^4.5.1"}}