<template>
  <div>
    <el-table :data="taskTableData">
      <el-table-column
        v-for="item in taskTableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        :width="item.width || 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.value === 'startTime'">
            {{ formatDateTime(row[item.value]) }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <h3>文件列表</h3>

    <!-- 文件路径 -->
    <div class="file-path">
      <div>路径：</div>
      <div
        v-for="(item, index) in pathArray"
        :key="index"
        class="is-direcotry"
        @click="handlePathClick(index)"
      >
        {{ item + " / " }}
      </div>
    </div>
    <el-table :data="paginatedFileData">
      <el-table-column
        v-for="item in fileTableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
      >
        <template #default="{ row }">
          <div v-if="item.value === 'filename'">
            <span
              v-if="row.direcotry"
              class="is-direcotry"
              @click="handleNext(row)"
              >{{ row.filename }} /</span
            >
            <span v-else>{{ row.filename }}</span>
          </div>
          <span v-else-if="item.value === 'length'">
            {{ formatFileSize(row[item.value]) }}
          </span>
          <span v-else-if="item.value === 'lastModified'">
            {{ new Date(row[item.value]).toLocaleString() }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250">
        <template #default="{ row }">
          <!-- 查看文件 -->
          <el-button
            v-if="!row.direcotry && !row.filename.endsWith('.vtk')"
            @click="handleShowCode(row)"
            type="primary"
            size="small"
          >
            <el-icon><View /></el-icon>
          </el-button>
          <!-- 查看趋势图 -->
          <el-button
            v-if="row.filename == 'SUMMARY.out'"
            type="primary"
            size="small"
            @click="handleShowChart(row)"
          >
            <el-icon><TrendCharts /></el-icon>
          </el-button>
          <el-button
            v-if="!row.direcotry"
            type="primary"
            size="small"
            @click="handleDownload(row)"
          >
            <el-icon><Download /></el-icon>
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-wrapper" v-if="fileTableData.length > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="fileTableData.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <el-dialog v-model="innerVisible" width="60%" title="详情" append-to-body>
    <!-- 代码内容显示 -->
    <div v-if="contentType === 'code'" class="code-container">
      <div class="code-header">
        <span class="file-name">{{ currentFileName }}</span>
        <el-button size="small" @click="copyToClipboard">复制代码</el-button>
      </div>
      <div class="code-content">
        <pre class="code-block"><code>{{ fileContent }}</code></pre>
      </div>
    </div>

    <!-- 图表内容显示 -->
    <ChartLine
      v-if="contentType === 'chart'"
      :data="chartData"
      title="气体生产与注入数据"
      chart-height="500px"
    />
  </el-dialog>
</template>

<script setup>
import {
  to,
  job_detail,
  file_list,
  file_delete,
  file_summary,
  file_content,
  file_download,
} from "@/api/index";
import { formatDateTime } from "@/utils/index.js";

const props = defineProps({
  taskId: {
    type: String,
    required: true,
  },
});

const innerVisible = ref(false);
const contentType = ref("chart");

// 任务详情
const taskTableData = ref([]);
const taskTableItems = ref([
  { label: "任务ID", value: "taskId", width: "250px" },
  { label: "任务名称", value: "jobName" },
  { label: "节点数", value: "nnodes" },
  { label: "核数", value: "ncpus" },
  { label: "起始时间", value: "startTime", width: "180px" },
  { label: "运行时间", value: "elapsed" },
  { label: "作业状态", value: "state", width: "180px" },
]);

// 目录列表
const fileTableData = ref([]);
const fileTableItems = ref([
  { label: "文件名", value: "filename" },
  { label: "大小", value: "length" },
  { label: "修改时间", value: "lastModified" },
]);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);

// 计算分页后的数据
const paginatedFileData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return fileTableData.value.slice(start, end);
});

// 获取任务详情
const fetchDetail = async () => {
  const [err, res] = await to(() => job_detail({ taskId: props.taskId }));
  if (err) {
    ElMessage.error("获取任务详情失败");
    return;
  }
  taskTableData.value = res.data || [];
};

const pathArray = ref([props.taskId]);

const fetchFileList = async () => {
  const [err, res] = await to(() =>
    file_list({ pathUrl: pathArray.value.join("/") })
  );
  if (err) {
    ElMessage.error("获取文件列表失败");
    return;
  }
  fileTableData.value = res.data || [];
  // 重置分页到第一页
  currentPage.value = 1;
};

const handlePathClick = (index) => {
  pathArray.value = pathArray.value.slice(0, index + 1);
  fetchFileList();
};

const handleNext = (row) => {
  if (row.direcotry) {
    pathArray.value.push(row.filename);
    fetchFileList();
  }
};

const fileContent = ref("");
const currentFileName = ref("");

const handleShowCode = async (row) => {
  const [err, res] = await to(() =>
    file_content({ fileFullName: `${currentPath.value}/${row.filename}` })
  );
  if (err) {
    ElMessage.error("获取文件内容失败");
    return;
  }
  console.log(err, res);
  innerVisible.value = true;
  contentType.value = "code";
  currentFileName.value = row.filename;
  // 直接使用res，因为axios拦截器现在会直接返回文件内容
  fileContent.value = res || "无内容";
};

// 复制代码到剪贴板
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(fileContent.value);
    ElMessage.success("代码已复制到剪贴板");
  } catch (err) {
    // 降级处理
    const textArea = document.createElement("textarea");
    textArea.value = fileContent.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
    ElMessage.success("代码已复制到剪贴板");
  }
};

// 图表数据显示
const chartData = ref({});
const handleShowChart = async (row) => {
  const [err, res] = await to(() =>
    file_summary({ fileFullName: `${currentPath.value}/${row.filename}` })
  );
  chartData.value = res.data || {};
  contentType.value = "chart";
  innerVisible.value = true;
};

// 下载文件
const handleDownload = async (row) => {
  const [err] = await to(() =>
    file_download({ fileFullName: `${currentPath.value}/${row.filename}` })
  );
  if (err) {
    ElMessage.error("下载文件失败");
    return;
  }
};

// 删除文件
const handleDelete = (row) => {
  ElMessageBox.confirm("确认删除该文件吗？", "提示", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const [err] = await to(() =>
        file_delete({ pathUrl: `${currentPath.value}/${row.filename}` })
      );
      if (err) {
        ElMessage.error("删除文件失败");
        return;
      }
      fileTableData.value = fileTableData.value.filter(
        (item) => item.filename !== row.filename
      );
      ElMessage.success("文件删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return "0 B";

  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  if (i === 0) {
    return bytes + " B";
  }

  return (bytes / Math.pow(1024, i)).toFixed(2) + " " + sizes[i];
};

// 分页事件处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 切换每页数量时回到第一页
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 计算当前路径
const currentPath = computed(() => {
  return pathArray.value.join("/");
});

watch(
  () => props.taskId,
  (newTaskId, oldTaskId) => {
    if (newTaskId && newTaskId !== oldTaskId) {
      taskTableData.value = [];
      fileTableData.value = [];
      // 重置分页状态
      currentPage.value = 1;
      pageSize.value = 10;
      fetchDetail();
      fetchFileList();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.file-path {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.is-direcotry {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

// 代码显示样式
.code-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #666;

  .file-name {
    color: #000000;
    font-size: 14px;
    font-weight: 500;
  }
}

.code-content {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
}

.code-block {
  margin: 0;
  padding: 16px;
  background: #f5f5f5;
  color: #000000;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;

  code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
  }
}

// 自定义滚动条样式
.code-content::-webkit-scrollbar {
  width: 3px;
  height: 6px;
}

.code-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.code-content::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 6px;

  &:hover {
    background: #4f4f4f;
  }
}

.code-content::-webkit-scrollbar-corner {
  background: #1e1e1e;
}

// 响应式设计
@media (max-width: 768px) {
  .code-container {
    height: 60vh;
  }

  .code-block {
    padding: 12px;
    font-size: 12px;
  }

  .code-header {
    padding: 8px 12px;

    .file-name {
      font-size: 13px;
    }
  }
}
</style>
