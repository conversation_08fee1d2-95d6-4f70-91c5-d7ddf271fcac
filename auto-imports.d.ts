// Generated by 'unplugin-auto-import'
export {}
declare global {
  const Search: typeof import('@element-plus/icons-vue')['Search']
  const Edit: typeof import('@element-plus/icons-vue')['Edit']
  const Delete: typeof import('@element-plus/icons-vue')['Delete']
  const Plus: typeof import('@element-plus/icons-vue')['Plus']
  const View: typeof import('@element-plus/icons-vue')['View']
  const Setting: typeof import('@element-plus/icons-vue')['Setting']
  const Download: typeof import('@element-plus/icons-vue')['Download']
  const Close: typeof import('@element-plus/icons-vue')['Close']
  const Check: typeof import('@element-plus/icons-vue')['Check']
  const Message: typeof import('@element-plus/icons-vue')['Message']
  const RefreshRight: typeof import('@element-plus/icons-vue')['RefreshRight']
  const ArrowDown: typeof import('@element-plus/icons-vue')['ArrowDown']
  const ArrowUp: typeof import('@element-plus/icons-vue')['ArrowUp']
  const Back: typeof import('@element-plus/icons-vue')['Back']
  const Right: typeof import('@element-plus/icons-vue')['Right']
  const Loading: typeof import('@element-plus/icons-vue')['Loading']
  const MoreFilled: typeof import('@element-plus/icons-vue')['MoreFilled']
  const Star: typeof import('@element-plus/icons-vue')['Star']
  const Calendar: typeof import('@element-plus/icons-vue')['Calendar']
  const User: typeof import('@element-plus/icons-vue')['User']
  const Lock: typeof import('@element-plus/icons-vue')['Lock']
  const Folder: typeof import('@element-plus/icons-vue')['Folder']
  const Document: typeof import('@element-plus/icons-vue')['Document']
  const Share: typeof import('@element-plus/icons-vue')['Share']
  const Upload: typeof import('@element-plus/icons-vue')['Upload']
  const Filter: typeof import('@element-plus/icons-vue')['Filter']
  const Sort: typeof import('@element-plus/icons-vue')['Sort']
  const Menu: typeof import('@element-plus/icons-vue')['Menu']
  const Location: typeof import('@element-plus/icons-vue')['Location']
  const InfoFilled: typeof import('@element-plus/icons-vue')['InfoFilled']
  
  // Vue
  const ref: typeof import('vue')['ref']
  const reactive: typeof import('vue')['reactive']
  const computed: typeof import('vue')['computed']
  const watch: typeof import('vue')['watch']
  const onMounted: typeof import('vue')['onMounted']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const nextTick: typeof import('vue')['nextTick']
  const defineProps: typeof import('vue')['defineProps']
  const defineEmits: typeof import('vue')['defineEmits']
  const defineExpose: typeof import('vue')['defineExpose']
  
  // Pinia
  const defineStore: typeof import('pinia')['defineStore']
  const storeToRefs: typeof import('pinia')['storeToRefs']
}
