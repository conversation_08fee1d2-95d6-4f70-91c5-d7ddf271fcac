<template>
  <el-tabs v-model="activeName">
    <el-tab-pane
      v-for="item in tabs"
      :label="item.label"
      :name="item.name"
      :key="item.name"
    >
      <!-- 文件列表 -->
      <div v-if="activeName === 'file'">
        <h3>文件列表</h3>
        <!-- 文件路径 -->
        <div class="file-path">
          <div>路径：</div>
          <div
            v-for="(item, index) in pathArray"
            :key="index"
            class="is-direcotry"
            @click="handlePathClick(index)"
          >
            {{ item + (index == 0 ? "" : "/") }}
          </div>
        </div>
        <el-table :data="paginatedFileData">
          <el-table-column
            v-for="item in fileTableItems"
            :key="item.value"
            :prop="item.value"
            :label="item.label"
          >
            <template #default="{ row }">
              <div v-if="item.value === 'filename'">
                <span
                  v-if="row.direcotry"
                  class="is-direcotry"
                  @click="handleNext(row)"
                  >{{ row.filename }}/</span
                >
                <span v-else @click="handleDownload(row)">{{
                  row.filename
                }}</span>
              </div>
              <span v-else-if="item.value === 'length'">
                {{ formatFileSize(row[item.value]) }}
              </span>
              <span v-else-if="item.value === 'lastModified'">
                {{ new Date(row[item.value]).toLocaleString() }}
              </span>
              <span v-else>{{ row[item.value] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="danger" size="small" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-if="fileTableData.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="fileTableData.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <!-- 上传文件 -->
      <div v-if="activeName === 'upload'">
        <h3>上传文件到：{{ currentPath }}</h3>
        <div class="upload-container">
          <!-- 选择文件按钮 -->
          <div class="upload-actions">
            <el-button type="primary" @click="selectFiles">
              <el-icon><Document /></el-icon>
              选择文件
            </el-button>
            <el-button type="primary" @click="selectFolder">
              <el-icon><Folder /></el-icon>
              选择文件夹
            </el-button>
          </div>

          <!-- 拖拽上传区域 -->
          <div
            class="upload-drop-zone"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            :class="{ 'is-dragover': isDragOver }"
          >
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
              将文件拖拽到此处，或点击上方按钮选择文件
            </div>
          </div>

          <!-- 文件列表 -->
          <div v-if="selectedFiles.length > 0" class="file-list">
            <h4>待上传文件 ({{ selectedFiles.length }})</h4>
            <div
              class="file-item"
              v-for="(file, index) in selectedFiles"
              :key="index"
            >
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
              <el-button
                type="danger"
                size="small"
                text
                @click="removeFile(index)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 上传按钮 -->
          <div class="upload-controls" v-if="selectedFiles.length > 0">
            <el-button
              type="success"
              :loading="uploading"
              @click="handleUpload"
            >
              <el-icon><Upload /></el-icon>
              {{
                uploading ? "上传中..." : `上传 ${selectedFiles.length} 个文件`
              }}
            </el-button>
            <el-button @click="clearFiles">清空</el-button>
          </div>

          <!-- 隐藏的 input 元素 -->
          <input
            ref="fileInput"
            type="file"
            multiple
            style="display: none"
            @change="handleFileSelect"
          />
          <input
            ref="folderInput"
            type="file"
            webkitdirectory
            style="display: none"
            @change="handleFolderSelect"
          />
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Document,
  Folder,
  UploadFilled,
  Upload,
  Close,
  Delete,
} from "@element-plus/icons-vue";
import {
  to,
  file_list,
  file_download,
  file_delete,
  file_upload,
} from "@/api/index.js";

// tabs
const activeName = ref("file");
const tabs = ref([
  { label: "我的文件", name: "file" },
  { label: "上传文件", name: "upload" },
]);

const pathArray = ref(["/"]);

// 目录列表
const fileTableData = ref([]);
const fileTableItems = ref([
  { label: "文件名", value: "filename" },
  { label: "大小", value: "length" },
  { label: "修改时间", value: "lastModified" },
]);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);

// 上传相关状态
const selectedFiles = ref([]);
const uploading = ref(false);
const isDragOver = ref(false);

// DOM 引用
const fileInput = ref();
const folderInput = ref();

// 计算分页后的数据
const paginatedFileData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return fileTableData.value.slice(start, end);
});

// 计算当前路径
const currentPath = computed(() => {
  const path = pathArray.value.join("/");
  return path === "/" ? "/" : path;
});

// 工具函数：构建完整文件路径
const buildFilePath = (filename) => {
  return currentPath.value === "/"
    ? `/${filename}`
    : `${currentPath.value}/${filename}`;
};

// 文件选择功能
const selectFiles = () => {
  // 使用 nextTick 确保 DOM 更新完成
  nextTick(() => {
    try {
      console.log("selectFiles - fileInput.value:", fileInput.value);
      console.log("fileInput.value type:", typeof fileInput.value);
      console.log("fileInput.value.click type:", typeof fileInput.value?.click);

      // 多重检查确保 DOM 元素存在且可用
      if (
        fileInput.value &&
        fileInput.value instanceof HTMLElement &&
        typeof fileInput.value.click === "function"
      ) {
        fileInput.value.click();
      } else {
        // 备用方案：手动创建 input 元素
        console.warn("使用备用方案创建文件选择器");
        const input = document.createElement("input");
        input.type = "file";
        input.multiple = true;
        input.style.display = "none";
        input.addEventListener("change", handleFileSelect);
        document.body.appendChild(input);
        input.click();
        document.body.removeChild(input);
      }
    } catch (error) {
      console.error("selectFiles error:", error);
      ElMessage.error("文件选择器打开失败");
    }
  });
};

const selectFolder = () => {
  nextTick(() => {
    try {
      console.log("selectFolder - folderInput.value:", folderInput.value);
      console.log("folderInput.value type:", typeof folderInput.value);
      console.log(
        "folderInput.value.click type:",
        typeof folderInput.value?.click
      );

      // 多重检查确保 DOM 元素存在且可用
      if (
        folderInput.value &&
        folderInput.value instanceof HTMLElement &&
        typeof folderInput.value.click === "function"
      ) {
        folderInput.value.click();
      } else {
        // 备用方案：手动创建 input 元素
        console.warn("使用备用方案创建文件夹选择器");
        const input = document.createElement("input");
        input.type = "file";
        input.webkitdirectory = true;
        input.style.display = "none";
        input.addEventListener("change", handleFolderSelect);
        document.body.appendChild(input);
        input.click();
        document.body.removeChild(input);
      }
    } catch (error) {
      console.error("selectFolder error:", error);
      ElMessage.error("文件夹选择器打开失败");
    }
  });
};

const handleFileSelect = (event) => {
  try {
    const files = Array.from(event.target.files || []);
    console.log("选择的文件数量:", files.length);
    addFiles(files);

    // 清空input，允许重复选择同一文件
    if (event.target) {
      event.target.value = "";
    }
  } catch (error) {
    console.error("处理文件选择失败:", error);
    ElMessage.error("文件选择处理失败");
  }
};

const handleFolderSelect = (event) => {
  try {
    const files = Array.from(event.target.files || []);
    console.log("选择的文件夹文件数量:", files.length);
    addFiles(files);

    // 清空input
    if (event.target) {
      event.target.value = "";
    }
  } catch (error) {
    console.error("处理文件夹选择失败:", error);
    ElMessage.error("文件夹选择处理失败");
  }
};

const addFiles = (files) => {
  files.forEach((file) => {
    // 检查是否已存在同名文件
    const exists = selectedFiles.value.some((f) => f.name === file.name);
    if (!exists) {
      selectedFiles.value.push(file);
    }
  });
};

// 拖拽功能
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragEnter = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = Array.from(event.dataTransfer.files);
  addFiles(files);
};

// 移除文件
const removeFile = (index) => {
  selectedFiles.value.splice(index, 1);
};

// 清空文件列表
const clearFiles = () => {
  selectedFiles.value = [];
};

// 上传文件
const handleUpload = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning("请先选择文件");
    return;
  }

  uploading.value = true;
  let successCount = 0;
  let failCount = 0;

  for (const file of selectedFiles.value) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("path", currentPath.value);

    const [err] = await to(() => file_upload(formData));
    if (err) {
      failCount++;
    } else {
      successCount++;
    }
  }

  uploading.value = false;

  if (failCount === 0) {
    ElMessage.success(`成功上传 ${successCount} 个文件`);
    selectedFiles.value = [];
    fetchFileList(); // 刷新文件列表
  } else {
    ElMessage.warning(
      `上传完成：成功 ${successCount} 个，失败 ${failCount} 个`
    );
  }
};

const fetchFileList = async () => {
  const [err, res] = await to(() => file_list({ pathUrl: currentPath.value }));
  if (err) {
    ElMessage.error("获取文件列表失败");
    return;
  }
  fileTableData.value = res.data || [];
  currentPage.value = 1;
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return "0 B";
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return i === 0
    ? `${bytes} B`
    : `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
};

const handlePathClick = (index) => {
  pathArray.value = pathArray.value.slice(0, index + 1);
  fetchFileList();
};

const handleNext = (row) => {
  if (row.direcotry) {
    pathArray.value.push(row.filename);
    fetchFileList();
  }
};

// 分页处理函数
const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
};

// 下载文件
const handleDownload = async (row) => {
  const [err] = await to(() =>
    file_download({
      fileFullName: buildFilePath(row.filename),
      fileName: row.filename,
    })
  );
  if (err) {
    ElMessage.error("下载文件失败");
    return;
  }
  ElMessage.success("文件下载成功");
};

// 删除文件
const handleDelete = (row) => {
  ElMessageBox.confirm("确认删除该文件吗？", "提示", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const [err] = await to(() =>
        file_delete({ pathUrl: buildFilePath(row.filename) })
      );
      if (err) {
        ElMessage.error("删除文件失败");
        return;
      }
      fileTableData.value = fileTableData.value.filter(
        (item) => item.filename !== row.filename
      );
      ElMessage.success("文件删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

onMounted(() => {
  fetchFileList();
});
</script>

<style lang="scss" scoped>
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}

.upload-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.upload-drop-zone {
  width: 100%;
  max-width: 600px;
  min-height: 180px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-light);
  transition: all 0.3s;
  cursor: pointer;

  &:hover,
  &.is-dragover {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }

  .upload-icon {
    font-size: 48px;
    color: var(--el-color-info);
    margin-bottom: 12px;
  }

  .upload-text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center;
  }
}

.file-list {
  width: 100%;
  max-width: 600px;

  h4 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;
    margin-bottom: 8px;

    .file-name {
      flex: 1;
      color: var(--el-text-color-primary);
      font-size: 14px;
    }

    .file-size {
      color: var(--el-text-color-regular);
      font-size: 12px;
    }
  }
}

.upload-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.file-path {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.is-direcotry {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
