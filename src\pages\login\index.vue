<route lang="yaml">
meta:
  layout: default
</route>

<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-bg">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card">
      <!-- Logo 和标题 -->
      <div class="login-header">
        <h1 class="title">油气数据管理平台</h1>
        <p class="subtitle">欢迎登录系统</p>
      </div>

      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
            <!-- <el-link type="primary" :underline="false">忘记密码？</el-link> -->
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-btn"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? "登录中..." : "登录" }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 版权信息 -->
    <div class="copyright">
      <p>© 2025 油气数据管理平台 All Rights Reserved</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { userStore } from "@/stores/user";

const router = useRouter();
const useUserStore = userStore();

// 表单引用
const loginFormRef = ref();

// 表单数据
const loginForm = reactive({
  username: "",
  password: "",
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度在 3 到 20 个字符",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" },
  ],
};

// 状态管理
const loading = ref(false);
const rememberMe = ref(false);

// 登录处理
const handleLogin = async () => {
  try {
    // 表单验证
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 调用登录接口
    const loginData = {
      userName: loginForm.username.trim(),
      password: loginForm.password.trim(),
    };

    const success = await useUserStore.login(loginData);

    if (success) {
      ElMessage.success("登录成功");

      // 记住密码
      if (rememberMe.value) {
        localStorage.setItem("rememberedUsername", loginForm.username);
        localStorage.setItem("rememberedPassword", loginForm.password);
      } else {
        localStorage.removeItem("rememberedUsername");
        localStorage.removeItem("rememberedPassword");
      }

      // 跳转到首页或之前要访问的页面
      const redirect = router.currentRoute.value.query.redirect || "/";
      router.push(redirect);
    } else {
      ElMessage.error("用户名或密码错误");
    }
  } catch (error) {
    console.error("登录失败:", error);
    ElMessage.error("登录失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时恢复记住的密码
onMounted(() => {
  const rememberedUsername = localStorage.getItem("rememberedUsername");
  const rememberedPassword = localStorage.getItem("rememberedPassword");

  if (rememberedUsername && rememberedPassword) {
    loginForm.username = rememberedUsername;
    loginForm.password = rememberedPassword;
    rememberMe.value = true;
  }
});
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a59b0 0%, #2d7dd2 50%, #4ea5f9 100%);
  overflow: hidden;
}

// 背景装饰
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 登录卡片
.login-card {
  position: relative;
  z-index: 2;
  width: 420px;
  padding: 48px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 登录头部
.login-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a59b0;
    margin: 0 0 8px 0;
    line-height: 1.2;
  }

  .subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

// 登录表单
.login-form {
  .el-form-item {
    margin-bottom: 24px;

    :deep(.el-input) {
      height: 48px;

      .el-input__wrapper {
        border-radius: 12px;
        box-shadow: 0 0 0 1px #dcdfe6;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 0 1px #1a59b0;
        }

        &.is-focus {
          box-shadow: 0 0 0 2px rgba(26, 89, 176, 0.2);
        }
      }

      .el-input__inner {
        height: 46px;
        line-height: 46px;
        font-size: 16px;
      }
    }
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  :deep(.el-checkbox) {
    .el-checkbox__label {
      color: #666;
    }
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  background: linear-gradient(135deg, #1a59b0, #2d7dd2);
  border: none;
  transition: all 0.3s;

  &:hover {
    background: linear-gradient(135deg, #164d9c, #2670c4);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(26, 89, 176, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 版权信息
.copyright {
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;

  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-card {
    width: 90%;
    max-width: 380px;
    padding: 32px 24px;
  }

  .login-header {
    .title {
      font-size: 24px;
    }
  }
}
</style>
