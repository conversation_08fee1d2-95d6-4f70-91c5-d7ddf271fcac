<template>
  <div class="tasks-list">
    <h2>队列监控</h2>
    <el-table :data="tableData">
      <el-table-column
        v-for="item in tableItems"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        :width="item.width || 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.value === 'startTime'">
            {{ formatDateTime(row[item.value]) }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button @click="handleEdit(row)" type="primary" size="small">
            查看
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <el-pagination
      class="pagination-right"
      v-model:current-page="pages.currentPage"
      v-model:page-size="pages.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      background
      size="small"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pages.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="visibleTask"
      title="任务详情"
      width="80%"
      @close="visibleTask = false"
    >
      <task-detail :taskId="currentTaskId" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { to, queue_list } from "@/api/index.js";
import { formatDateTime } from "@/utils/index.js";
import TaskDetail from "@/components/Task/TaskDetail.vue";

// Props - 接收外部传入的统计数据更新函数
const props = defineProps({
  onStatsUpdate: {
    type: Function,
    default: () => {},
  },
});

// 原始数据存储
const allTableData = ref([]);

// 分页配置
const pages = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 当前页显示的数据
const tableData = computed(() => {
  const start = (pages.currentPage - 1) * pages.pageSize;
  const end = start + pages.pageSize;
  return allTableData.value.slice(start, end);
});

// 表格列配置
const tableItems = ref([
  { label: "HPC ID", value: "hpcId" },
  { label: "队列名", value: "partitionName" },
  { label: "状态", value: "state" },
  { label: "分配的节点", value: "allocNodes" },
  { label: "节点数", value: "totalNodes" },
  { label: "总GPU数", value: "totalGPUs" },
  { label: "总CPU数", value: "totalCPUs" },
  { label: "时间限制", value: "maxTime" },
]);

// 任务详情弹窗
const visibleTask = ref(false);
const currentTaskId = ref(null);

// 查看任务详情
const handleEdit = (row) => {
  currentTaskId.value = row.taskId;
  visibleTask.value = true;
};

// 页码改变处理
const handleCurrentChange = (page) => {
  pages.currentPage = page;
};

// 每页条数改变处理
const handleSizeChange = (size) => {
  pages.pageSize = size;
};

// 获取数据列表
const fetchList = async () => {
  const [err, res] = await to(() => queue_list());
  if (err) {
    ElMessage.error("获取数据失败");
    return;
  }

  // 存储所有数据
  allTableData.value = res.data || [];
  pages.total = allTableData.value.length;
  pages.currentPage = 1; // 重置到第一页

  // 计算统计数据并通知父组件
  const stats = {
    total: allTableData.value.length,
    nodes: allTableData.value.reduce(
      (acc, item) => acc + (Number(item.totalNodes) || 0),
      0
    ),
    gpus: allTableData.value.reduce(
      (acc, item) => acc + (Number(item.totalGPUs) || 0),
      0
    ),
    cpus: allTableData.value.reduce(
      (acc, item) => acc + (Number(item.totalCPUs) || 0),
      0
    ),
  };

  //   通知父组件更新统计数据;
  props.onStatsUpdate(stats);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchList();
});

// 暴露刷新方法给父组件
defineExpose({
  refresh: fetchList,
});
</script>

<style lang="scss" scoped>
.tasks-list {
  .pagination-right {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
