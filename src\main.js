// Plugins
import pinia from "./stores";
import router from "./router";

// 导入全局样式
import "element-plus/dist/index.css"; // 修改为直接引入编译后的CSS
import "@/assets/styles/global.scss";
import ElementPlus from "element-plus";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

import { createTerminal } from "vue-web-terminal";

import App from "./App.vue";
import { createApp } from "vue";

const app = createApp(App);

// 先初始化 Element Plus
app.use(ElementPlus);

// 再全局注册所有图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(pinia);
app.use(router);
app.use(createTerminal());

// 初始化应用
const initApp = async () => {
  // 挂载应用
  app.mount("#app");
};

// 启动应用
initApp();
