<template>
  <div>vtk</div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
<!-- <template>
  <div class="vtk-container">
    <div class="controls">
      <el-select
        v-model="selectedFile"
        @change="loadSelectedFile"
        placeholder="选择VTK文件"
      >
        <el-option
          v-for="file in vtkFiles"
          :key="file.value"
          :label="file.label"
          :value="file.value"
        />
      </el-select>
      <input type="file" @change="handleFileUpload" accept=".vtk" />
    </div>
    <div ref="vtkContainer" class="vtk-viewer"></div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from "vue";
import "@kitware/vtk.js/Rendering/Profiles/Geometry";
import vtkFullScreenRenderWindow from "@kitware/vtk.js/Rendering/Misc/FullScreenRenderWindow";
import vtkPolyDataReader from "@kitware/vtk.js/IO/Legacy/PolyDataReader";
import vtkGenericDataSetReader from "@kitware/vtk.js/IO/Legacy/DataSetReader";
import vtkMapper from "@kitware/vtk.js/Rendering/Core/Mapper";
import vtkActor from "@kitware/vtk.js/Rendering/Core/Actor";

export default {
  name: "VtkViewer",
  setup() {
    const vtkContainer = ref(null);
    const selectedFile = ref("/sphere.vtk");
    let fullScreenRenderer = null;
    let renderWindow = null;
    let renderer = null;

    // VTK文件列表
    const vtkFiles = ref([
      { value: "/sphere.vtk", label: "Sphere - 球体" },
      { value: "/cube.vtk", label: "Cube - 立方体" },
      { value: "/cylinder.vtk", label: "Cylinder - 圆柱体" },
      { value: "/pyramid.vtk", label: "Pyramid - 金字塔" },
      { value: "/tetrahedron.vtk", label: "Tetrahedron - 四面体" },
      { value: "/triangle.vtk", label: "Triangle - 三角形" },
      { value: "/points-with-data.vtk", label: "Points with Data - 数据点" },
      { value: "/simple-point.vtk", label: "Simple Point - 简单点" },
      { value: "/TSTEP0.vtk", label: "TSTEP0 - 时间步数据" },
    ]);

    // 初始化VTK渲染器
    const initializeRenderer = () => {
      if (!vtkContainer.value) return;

      fullScreenRenderer = vtkFullScreenRenderWindow.newInstance({
        rootContainer: vtkContainer.value,
        containerStyle: { width: "100%", height: "100%" },
      });
      renderWindow = fullScreenRenderer.getRenderWindow();
      renderer = fullScreenRenderer.getRenderer();

      // 设置背景色
      renderer.setBackground(0.1, 0.1, 0.1);
    }; // 清除当前场景
    const clearScene = () => {
      if (renderer) {
        const actors = renderer.getActors();
        actors.forEach((actor) => {
          renderer.removeActor(actor);
        });
      }
    };

    // 从URL加载VTK文件
    const loadVTKFromURL = async (url) => {
      if (!renderWindow) return;

      try {
        clearScene();

        console.log("Loading VTK file:", url);

        // 获取文件内容
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        console.log("VTK file content length:", text.length);
        console.log("VTK file header:", text.substring(0, 100)); // 尝试解析VTK文件，先用PolyDataReader，如果失败则用通用DataSetReader
        let reader = vtkPolyDataReader.newInstance();
        let polyData = null;

        try {
          reader.parseAsText(text);
          polyData = reader.getOutputData();
          console.log("Successfully parsed with PolyDataReader");
        } catch (parseError) {
          console.log("PolyDataReader failed, trying GenericDataSetReader...");
          console.error("PolyDataReader error:", parseError);

          try {
            reader = vtkGenericDataSetReader.newInstance();
            reader.parseAsText(text);
            polyData = reader.getOutputData();
            console.log("Successfully parsed with GenericDataSetReader");
          } catch (genericError) {
            console.error("GenericDataSetReader error:", genericError);
            throw new Error(
              `所有VTK读取器都失败了: PolyData(${parseError.message}), Generic(${genericError.message})`
            );
          }
        }

        if (!polyData) {
          throw new Error("所有读取器都返回空数据");
        }

        const numPoints = polyData.getNumberOfPoints();
        const numCells = polyData.getNumberOfCells();

        if (numPoints === 0) {
          throw new Error("VTK文件中没有找到点数据");
        }

        console.log("PolyData loaded:", polyData);
        console.log("Number of points:", numPoints);
        console.log("Number of cells:", numCells);

        // 创建mapper和actor
        const mapper = vtkMapper.newInstance();
        mapper.setInputData(polyData);

        const actor = vtkActor.newInstance();
        actor.setMapper(mapper);

        // 添加到渲染器
        renderer.addActor(actor);
        renderer.resetCamera();
        renderWindow.render();
        console.log("VTK file rendered successfully");
      } catch (error) {
        console.error("Error loading VTK file:", error);
        alert(`加载VTK文件失败: ${error.message}`);
      }
    };

    // 加载并显示VTK文件
    const loadVTKFile = (file) => {
      if (!renderWindow) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          clearScene();

          const text = event.target.result;
          console.log("Local VTK file content length:", text.length); // 尝试解析VTK文件，先用PolyDataReader，如果失败则用通用DataSetReader
          let vtkReader = vtkPolyDataReader.newInstance();
          let polyData = null;

          try {
            vtkReader.parseAsText(text);
            polyData = vtkReader.getOutputData();
            console.log("Local file: Successfully parsed with PolyDataReader");
          } catch (parseError) {
            console.log(
              "Local file: PolyDataReader failed, trying GenericDataSetReader..."
            );
            console.error("Local PolyDataReader error:", parseError);

            try {
              vtkReader = vtkGenericDataSetReader.newInstance();
              vtkReader.parseAsText(text);
              polyData = vtkReader.getOutputData();
              console.log(
                "Local file: Successfully parsed with GenericDataSetReader"
              );
            } catch (genericError) {
              console.error("Local GenericDataSetReader error:", genericError);
              throw new Error(
                `所有VTK读取器都失败了: PolyData(${parseError.message}), Generic(${genericError.message})`
              );
            }
          }

          if (!polyData) {
            throw new Error("所有读取器都返回空数据");
          }

          const numPoints = polyData.getNumberOfPoints();
          const numCells = polyData.getNumberOfCells();

          if (numPoints === 0) {
            throw new Error("VTK文件中没有找到点数据");
          }

          console.log("Local VTK file points:", numPoints);
          console.log("Local VTK file cells:", numCells);

          const mapper = vtkMapper.newInstance();
          mapper.setInputData(polyData);

          const actor = vtkActor.newInstance();
          actor.setMapper(mapper);

          renderer.addActor(actor);
          renderer.resetCamera();
          renderWindow.render();

          console.log("Local VTK file rendered successfully");
        } catch (error) {
          console.error("Error loading local VTK file:", error);
          alert(`加载VTK文件失败: ${error.message}`);
        }
      };

      reader.readAsText(file);
    };

    // 选择文件变化
    const loadSelectedFile = () => {
      loadVTKFromURL(selectedFile.value);
    };

    // 处理文件上传
    const handleFileUpload = (event) => {
      const file = event.target.files[0];
      if (file) {
        loadVTKFile(file);
      }
    };

    onMounted(() => {
      initializeRenderer();
      // 延迟加载默认文件
      setTimeout(() => {
        loadSelectedFile();
      }, 100);
    });

    onBeforeUnmount(() => {
      if (fullScreenRenderer) {
        fullScreenRenderer.delete();
      }
    });

    return {
      vtkContainer,
      selectedFile,
      vtkFiles,
      handleFileUpload,
      loadSelectedFile,
    };
  },
};
</script>

<style scoped>
.vtk-container {
  width: 100%;
  height: 600px;
  position: relative;
}

.controls {
  padding: 10px;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.vtk-viewer {
  width: 100%;
  height: calc(100% - 80px);
  border: 1px solid #ddd;
  border-radius: 8px;
}

input[type="file"] {
  padding: 5px;
}
</style> -->
