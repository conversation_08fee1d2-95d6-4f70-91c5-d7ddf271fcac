<template>
  <div class="dataset-detail-page">
    <!-- 数据集头部信息 -->
    <div class="dataset-header-section">
      <div class="container">
        <el-row :gutter="32">
          <el-col :span="4">
            <div class="dataset-image">
              <el-image
                :src="datasetInfo.image"
                :alt="datasetInfo.title"
                fit="cover"
              />
            </div>
          </el-col>
          <el-col :span="20">
            <div class="dataset-info">
              <div class="dataset-title-area">
                <h1 class="dataset-title">{{ datasetInfo.title }}</h1>
                <div class="dataset-tags">
                  <el-tag size="large" type="primary">{{
                    datasetInfo.category
                  }}</el-tag>
                  <el-tag size="large">{{ datasetInfo.dataType }}</el-tag>
                  <el-tag size="large" v-if="datasetInfo.isFree" type="success"
                    >免费</el-tag
                  >
                  <el-tag size="large" v-if="datasetInfo.isHot" type="danger"
                    >热门</el-tag
                  >
                </div>
              </div>

              <div class="dataset-stats">
                <div class="stat-item">
                  <el-icon><Download /></el-icon>
                  <span class="stat-value">{{
                    formatNumber(datasetInfo.downloads)
                  }}</span>
                  <span class="stat-label">下载量</span>
                </div>
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span class="stat-value">{{
                    formatNumber(datasetInfo.views)
                  }}</span>
                  <span class="stat-label">浏览量</span>
                </div>
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span class="stat-value">{{ datasetInfo.rating }}</span>
                  <span class="stat-label">评分</span>
                </div>
                <div class="stat-item">
                  <el-icon><Calendar /></el-icon>
                  <span class="stat-value">{{
                    formatDate(datasetInfo.updateTime)
                  }}</span>
                  <span class="stat-label">更新时间</span>
                </div>
              </div>

              <div class="dataset-actions">
                <el-button
                  type="primary"
                  size="large"
                  @click="handleDownload"
                  :loading="downloading"
                >
                  <el-icon><Download /></el-icon>
                  立即下载
                </el-button>
                <el-button
                  size="large"
                  @click="handleCollect"
                  :class="{ 'is-collected': isCollected }"
                >
                  <el-icon><Star /></el-icon>
                  {{ isCollected ? "已收藏" : "收藏" }}
                </el-button>
                <el-button size="large" @click="handleShare">
                  <el-icon><Share /></el-icon>
                  分享
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <el-row :gutter="32">
          <!-- 左侧主要内容 -->
          <el-col :span="18">
            <div class="content-tabs">
              <el-tabs v-model="activeTab" class="demo-tabs">
                <el-tab-pane label="数据集详情" name="detail">
                  <div class="tab-content">
                    <div
                      class="markdown-content"
                      v-html="renderedOverview"
                    ></div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="论文引用" name="citation">
                  <div class="tab-content">
                    <div class="section">
                      <h3>引用信息</h3>
                      <div class="content-block">
                        <div class="citation-box">
                          <pre>{{ datasetInfo.citation }}</pre>
                          <el-button
                            type="primary"
                            text
                            @click="copyCitation"
                            class="copy-btn"
                          >
                            <el-icon><CopyDocument /></el-icon>
                            复制引用
                          </el-button>
                        </div>
                      </div>
                    </div>

                    <div class="section">
                      <h3>相关论文</h3>
                      <div class="content-block">
                        <div
                          v-for="paper in datasetInfo.papers"
                          :key="paper.id"
                          class="paper-item"
                        >
                          <h4>{{ paper.title }}</h4>
                          <p class="authors">{{ paper.authors }}</p>
                          <p class="venue">
                            {{ paper.venue }} · {{ paper.year }}
                          </p>
                          <div class="paper-links">
                            <el-link :href="paper.url" target="_blank"
                              >查看论文</el-link
                            >
                            <el-link
                              :href="paper.codeUrl"
                              target="_blank"
                              v-if="paper.codeUrl"
                              >代码链接</el-link
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>

          <!-- 右侧信息栏 -->
          <el-col :span="6">
            <div class="sidebar">
              <!-- 数据集信息 -->
              <div class="info-card">
                <h3>数据集信息</h3>
                <div class="info-list">
                  <div class="info-item">
                    <span class="label">创建者：</span>
                    <span class="value">{{ datasetInfo.author }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">数据规模：</span>
                    <span class="value">{{ datasetInfo.scale }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">文件大小：</span>
                    <span class="value">{{ datasetInfo.fileSize }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">数据格式：</span>
                    <span class="value">{{ datasetInfo.format }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">许可协议：</span>
                    <span class="value">{{ datasetInfo.license }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">创建时间：</span>
                    <span class="value">{{
                      formatDate(datasetInfo.createTime)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: front
</route>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { marked } from "marked";
import {
  Download,
  View,
  Star,
  Calendar,
  Share,
  CopyDocument,
  ChatDotRound,
  Warning,
} from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();
const activeTab = ref("detail");
const downloading = ref(false);
const isCollected = ref(false);
const showFeedbackDialog = ref(false);

// 数据集信息
const datasetInfo = ref({
  title: "加载中...",
  description: "",
  downloads: 0,
  views: 0,
  rating: 0,
  updateTime: null,
  category: "",
  dataType: "",
  author: "",
  scale: "",
  fileSize: "",
  format: "",
  license: "",
  createTime: null,
});
const relatedDatasets = ref([]);

// 反馈表单
const feedbackForm = reactive({
  type: "",
  content: "",
});

// 模拟数据
const mockDatasetInfo = {
  id: 1,
  title: "ImageNet-1K 图像分类数据集",
  description: `
## 数据集简介

包含**1000个类别**的大规模图像分类数据集，是计算机视觉领域的经典基准数据集，广泛用于：

- 图像分类
- 目标检测
- 特征提取
- 预训练模型

该数据集包含超过**120万张**训练图像和**5万张**验证图像，涵盖了动物、植物、物体、场景等多个类别。

### 主要特点

1. **规模大**：超过120万张高质量图像
2. **类别丰富**：1000个不同的对象类别
3. **标注准确**：专业人工标注和验证
4. **应用广泛**：深度学习模型的标准基准
  `,
  overview: `
# ImageNet 数据集概述

**ImageNet** 是一个大规模视觉识别挑战赛（ILSVRC）使用的图像数据库。

## 项目背景

ImageNet项目是一个大型视觉数据库，用于视觉对象识别软件研究。该项目的主要成就包括：

- 手动标注了超过 **1400万张** 图像
- 提供了精确的对象边界框标注
- 包含超过 **2万个** 不同类别
- 推动了深度学习在计算机视觉领域的发展

## 数据集构成

### 训练集
- **图像数量**：1,281,167张
- **分辨率**：可变（最小256像素）
- **格式**：JPEG

### 验证集
- **图像数量**：50,000张
- **用途**：模型性能评估
- **标注**：单一正确类别标签

## 使用场景

ImageNet 数据集广泛应用于：

1. **预训练模型**
   - ResNet、VGG、Inception等经典网络
   - 迁移学习的基础模型

2. **学术研究**
   - 计算机视觉算法评估
   - 新模型架构验证

3. **工业应用**
   - 图像识别系统
   - 智能相册分类
   - 自动驾驶视觉感知

## 重要里程碑

- **2012年**：AlexNet在ImageNet上取得突破性成果
- **2015年**：ResNet实现超人类准确率
- **至今**：仍是CV领域最重要的基准数据集

> 💡 **提示**：ImageNet的成功极大地推动了深度学习的发展，被誉为"AI的ImageNet时刻"。
  `,
  category: "图像分类",
  dataType: "图像",
  author: "Stanford Vision Lab",
  scale: "120万张图像",
  fileSize: "150 GB",
  format: "JPEG图像 + 标注文件",
  license: "自定义许可协议",
  createTime: new Date("2012-01-01"),
  updateTime: new Date("2024-01-15"),
  downloads: 125000,
  views: 580000,
  rating: 4.8,
  isFree: true,
  isHot: true,
  image:
    "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  structure: [
    { name: "train/", type: "目录", size: "138 GB", description: "训练集图像" },
    { name: "val/", type: "目录", size: "6.3 GB", description: "验证集图像" },
    {
      name: "train_labels.txt",
      type: "文件",
      size: "12 MB",
      description: "训练集标签",
    },
    {
      name: "val_labels.txt",
      type: "文件",
      size: "1.2 MB",
      description: "验证集标签",
    },
    {
      name: "class_names.txt",
      type: "文件",
      size: "12 KB",
      description: "类别名称映射",
    },
  ],
  citation: `@article{imagenet2012,
  title={ImageNet Large Scale Visual Recognition Challenge},
  author={Russakovsky, Olga and Deng, Jia and Su, Hao and Krause, Jonathan},
  journal={International Journal of Computer Vision},
  volume={115},
  number={3},
  pages={211--252},
  year={2015},
  publisher={Springer}
}`,
  papers: [
    {
      id: 1,
      title: "ImageNet Large Scale Visual Recognition Challenge",
      authors: "Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause",
      venue: "International Journal of Computer Vision",
      year: 2015,
      url: "https://arxiv.org/abs/1409.0575",
      codeUrl: "https://github.com/pytorch/examples",
    },
  ],
  versions: [
    {
      version: "1.0",
      date: new Date("2024-01-15"),
      description: "初始版本发布，包含完整的训练和验证数据",
      size: "150 GB",
      downloads: 125000,
    },
    {
      version: "0.9",
      date: new Date("2023-12-01"),
      description: "预发布版本，用于测试和反馈收集",
      size: "148 GB",
      downloads: 15000,
    },
  ],
};

const mockRelatedDatasets = [
  {
    id: 2,
    title: "COCO 2017",
    category: "目标检测",
    downloads: 89000,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
  {
    id: 3,
    title: "CIFAR-10",
    category: "图像分类",
    downloads: 156000,
    image:
      "https://baai-datasets.ks3-cn-beijing.ksyuncs.com/public_static/others/common_covers/dataset_title_images/66894156345114682.png",
  },
];

// 工具方法
const formatDate = (date) => {
  // 添加空值检查
  if (!date) {
    return "-";
  }
  return dayjs(date).format("YYYY-MM-DD");
};

const formatNumber = (num) => {
  // 添加空值检查
  if (num === undefined || num === null) {
    return "0";
  }

  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Markdown 渲染方法
const renderMarkdown = (content) => {
  if (!content) return "";
  return marked(content);
};

// 计算属性用于渲染 Markdown 内容
const renderedOverview = computed(() => {
  return renderMarkdown(datasetInfo.value.overview);
});

const renderedDescription = computed(() => {
  return renderMarkdown(datasetInfo.value.description);
});

// 事件处理方法
const handleDownload = () => {
  downloading.value = true;
  // 模拟下载过程
  setTimeout(() => {
    downloading.value = false;
    ElMessage.success("数据集下载链接已发送到您的邮箱");
  }, 2000);
};

const handleCollect = () => {
  isCollected.value = !isCollected.value;
  ElMessage.success(isCollected.value ? "收藏成功" : "取消收藏");
};

const handleShare = () => {
  // 复制当前页面链接到剪贴板
  navigator.clipboard.writeText(window.location.href);
  ElMessage.success("链接已复制到剪贴板");
};

const copyCitation = () => {
  navigator.clipboard.writeText(datasetInfo.value.citation);
  ElMessage.success("引用信息已复制到剪贴板");
};

const goToDataset = (id) => {
  router.push(`/dataset/detail/${id}`);
};

const submitFeedback = () => {
  if (!feedbackForm.type || !feedbackForm.content) {
    ElMessage.warning("请填写完整的反馈信息");
    return;
  }

  // 模拟提交反馈
  setTimeout(() => {
    ElMessage.success("反馈提交成功，感谢您的建议！");
    showFeedbackDialog.value = false;
    feedbackForm.type = "";
    feedbackForm.content = "";
  }, 1000);
};

const reportDataset = () => {
  ElMessage.info("举报功能开发中，如有问题请联系客服");
};

const loadDatasetInfo = async () => {
  const id = route.params.id;

  // 模拟API调用
  setTimeout(() => {
    datasetInfo.value = mockDatasetInfo;
    relatedDatasets.value = mockRelatedDatasets;
  }, 500);
};

// 初始化
onMounted(() => {
  loadDatasetInfo();
});
</script>

<style lang="scss" scoped>
.dataset-detail-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

// 数据集头部
.dataset-header-section {
  background: white;
  padding: 32px 0;
  border-bottom: 1px solid #e5e7eb;

  .dataset-info {
    .dataset-title-area {
      margin-bottom: 20px;

      .dataset-title {
        font-size: 32px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 12px 0;
        line-height: 1.2;
      }

      .dataset-tags {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
    .dataset-description {
      margin-bottom: 24px;

      .markdown-content {
        font-size: 16px;
        line-height: 1.6;
        color: #4b5563;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: #1f2937;
          margin: 1.5em 0 0.5em 0;
          font-weight: 600;
        }

        h1 {
          font-size: 1.8em;
        }
        h2 {
          font-size: 1.6em;
        }
        h3 {
          font-size: 1.4em;
        }
        h4 {
          font-size: 1.2em;
        }

        p {
          margin: 0.8em 0;
        }

        ul,
        ol {
          margin: 0.8em 0;
          padding-left: 1.5em;
        }

        li {
          margin: 0.3em 0;
        }

        strong {
          color: #1f2937;
          font-weight: 600;
        }

        code {
          background: #f3f4f6;
          padding: 0.2em 0.4em;
          border-radius: 3px;
          font-size: 0.9em;
          color: #e53e3e;
        }

        blockquote {
          border-left: 4px solid #3b82f6;
          background: #f0f9ff;
          margin: 1em 0;
          padding: 0.8em 1.2em;
          color: #1e40af;

          p {
            margin: 0;
          }
        }
      }
    }

    .dataset-stats {
      display: flex;
      gap: 32px;
      margin-bottom: 24px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-icon {
          font-size: 20px;
          color: #6b7280;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
        }
      }
    }

    .dataset-actions {
      display: flex;
      gap: 12px;

      .is-collected {
        color: #f59e0b;
        border-color: #f59e0b;
      }
    }
  }

  .dataset-image {
    .el-image {
      width: 100%;
      height: 150px;
      border-radius: 12px;
      overflow: hidden;
    }
  }
}

// 主要内容
.main-content {
  padding: 32px 0;

  .content-tabs {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .tab-content {
      padding-top: 24px;

      .section {
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #e5e7eb;
        }

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #374151;
          margin: 16px 0 8px 0;
        }
        .content-block {
          background: #f9fafb;
          border-radius: 8px;
          padding: 20px;

          p {
            line-height: 1.6;
            color: #4b5563;
            margin: 0;
          }

          .markdown-content {
            line-height: 1.6;
            color: #4b5563;

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
              color: #1f2937;
              margin: 1.5em 0 0.8em 0;
              font-weight: 600;
            }

            h1 {
              font-size: 1.8em;
            }
            h2 {
              font-size: 1.6em;
            }
            h3 {
              font-size: 1.4em;
            }
            h4 {
              font-size: 1.2em;
            }

            p {
              margin: 0.8em 0;
            }

            ul,
            ol {
              margin: 0.8em 0;
              padding-left: 1.5em;
            }

            li {
              margin: 0.3em 0;
            }

            strong {
              color: #1f2937;
              font-weight: 600;
            }

            code {
              background: #e5e7eb;
              padding: 0.2em 0.4em;
              border-radius: 3px;
              font-size: 0.9em;
              color: #dc2626;
              font-family: "Courier New", monospace;
            }

            blockquote {
              border-left: 4px solid #3b82f6;
              background: #f0f9ff;
              margin: 1em 0;
              padding: 0.8em 1.2em;
              color: #1e40af;
              border-radius: 4px;

              p {
                margin: 0;
              }
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin: 1em 0;
            }

            th,
            td {
              border: 1px solid #e5e7eb;
              padding: 0.5em 0.8em;
              text-align: left;
            }

            th {
              background: #f3f4f6;
              font-weight: 600;
              color: #1f2937;
            }
          }
        }
      }
    }

    .sample-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .sample-item {
        text-align: center;

        .el-image {
          width: 100%;
          height: 150px;
          border-radius: 8px;
        }

        .sample-label {
          margin-top: 8px;
          font-size: 14px;
          color: #6b7280;
        }
      }
    }

    .usage-guide {
      .el-steps {
        margin: 16px 0;
      }
    }

    .citation-box {
      position: relative;
      background: #f3f4f6;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      padding: 16px;

      pre {
        margin: 0;
        font-family: "Courier New", monospace;
        font-size: 14px;
        line-height: 1.4;
        color: #374151;
        white-space: pre-wrap;
      }

      .copy-btn {
        position: absolute;
        top: 8px;
        right: 8px;
      }
    }

    .paper-item {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      margin-bottom: 16px;

      h4 {
        margin: 0 0 8px 0;
        color: #1f2937;
      }

      .authors {
        color: #6b7280;
        margin: 0 0 4px 0;
      }

      .venue {
        color: #9ca3af;
        font-size: 14px;
        margin: 0 0 12px 0;
      }

      .paper-links {
        display: flex;
        gap: 16px;
      }
    }

    .version-info {
      h4 {
        margin: 0 0 8px 0;
        color: #1f2937;
      }

      p {
        margin: 0 0 8px 0;
        color: #6b7280;
      }

      .version-stats {
        display: flex;
        gap: 16px;
        font-size: 14px;
        color: #9ca3af;
      }
    }
  }
}

// 侧边栏
.sidebar {
  .info-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 1px solid #e5e7eb;
    }

    .info-list {
      .info-item {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 80px;
          color: #6b7280;
          font-size: 14px;
        }

        .value {
          flex: 1;
          color: #1f2937;
          font-size: 14px;
        }
      }
    }

    .related-datasets {
      .related-item {
        display: flex;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: #f9fafb;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .related-image {
          width: 60px;
          height: 60px;
          border-radius: 6px;
          flex-shrink: 0;
        }

        .related-info {
          flex: 1;

          h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 4px 0;
            line-height: 1.3;
          }

          p {
            font-size: 12px;
            color: #6b7280;
            margin: 0 0 4px 0;
          }

          .related-stats {
            font-size: 12px;
            color: #9ca3af;
          }
        }
      }
    }

    .feedback-section {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dataset-header-section {
    .el-row {
      flex-direction: column-reverse;
    }

    .dataset-image {
      margin-bottom: 24px;
    }

    .dataset-stats {
      flex-wrap: wrap;
      gap: 16px;
    }

    .dataset-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }

  .main-content {
    .el-row {
      flex-direction: column;
    }

    .sidebar {
      margin-top: 24px;
    }
  }
}
</style>
