<template>
  <el-scrollbar height="100vh">
    <div class="stencil-item" v-for="item in stencilData" :key="item.id">
      <div class="stencil-title">
        <h5>{{ item.className }}</h5>
      </div>
      <div
        class="model-item"
        v-for="child in item.children"
        :key="child.id"
        @dragstart="dragStart($event, child)"
        draggable="true"
      >
        <div class="model-content">
          <div class="class-content">
            <div
              class="class-header"
              :class="{
                abstract: child.isAbstract,
                interface: child.isInterface,
              }"
            >
              {{ child.title }}
            </div>
            <div
              class="class-attrs"
              v-if="child.dataFiles && child.dataFiles.length"
            >
              <div v-for="(attr, idx) in child.dataFiles" :key="idx">
                {{ attr }}
              </div>
            </div>
            <div
              class="class-methods"
              v-if="child.methods && child.methods.length"
            >
              <div v-for="(method, idx) in child.methods" :key="idx">
                {{ method }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { Operation } from "@element-plus/icons-vue";
import { ref } from "vue";
const stencilData = ref([
  {
    id: "1",
    className: "模型",
    children: [
      {
        id: "1-1",
        title: "组分模型",
        nodeType: "class",
        name: ["组分模型"],
        dataFiles: ["+文件", "+文件"],
        methods: ["+参数()", "+结果()"],
        isAbstract: true,
      },
      {
        id: "1-2",
        title: "黑油模型",
        nodeType: "class",
        name: ["黑油模型"],
        dataFiles: ["+文件"],
        methods: ["+参数()", "+结果()"],
        isAbstract: true,
      },
      {
        id: "1-3",
        title: "热采模型",
        nodeType: "class",
        name: ["热采模型"],
        dataFiles: ["+文件"],
        methods: ["+参数()", "+结果()"],
        isAbstract: true,
      },
      {
        id: "1-4",
        title: "等温模型",
        nodeType: "class",
        name: ["等温模型"],
        dataFiles: ["+文件"],
        methods: ["+参数()", "+结果()"],
        isAbstract: true,
      },
    ],
  },
]);

const emit = defineEmits(["dragStart"]);

function dragStart(event, item) {
  event.dataTransfer.setData("text/plain", JSON.stringify(item));
  event.dataTransfer.effectAllowed = "copy";
  event.dataTransfer.dropEffect = "copy";
  emit("dragStart", event, item);
}
</script>

<style lang="scss" scoped>
.stencil-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  .stencil-title {
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: aqua;
    height: 40px;
  }
  .model-item {
    .model-content {
      font-size: 14px;
      padding: 15px;

      .class-content {
        display: flex;
        flex-direction: column;

        .class-header {
          text-align: center;
          padding: 5px;
          background-color: #f0f2f5;
          border-bottom: 1px solid #dfe3e8;
          font-weight: bold;

          &.abstract {
            font-style: italic;
            color: #666;
          }

          &.interface {
            background-color: #e6f7ff;
          }
        }

        .class-attrs,
        .class-methods {
          padding: 5px;
          border-bottom: 1px solid #f0f2f5;

          div {
            margin: 3px 0;
            font-size: 12px;
          }
        }

        .class-attrs {
          background-color: #fafafa;
        }

        .class-methods {
          background-color: #f5f5f5;
        }
      }
    }
  }
}
</style>
