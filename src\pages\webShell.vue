<script setup lang="ts">
import {
  Terminal,
  FailedFunc,
  SuccessFunc,
  TerminalMessageClass,
} from "vue-web-terminal";

const onExecCmd = (
  key: string,
  command: string,
  success: SuccessFunc,
  failed: FailedFunc
) => {
  if (key === "fail") {
    failed("Something wrong!!!");
  } else {
    let allClass = ["success", "error", "system", "info", "warning"];

    let clazz = allClass[Math.floor(Math.random() * allClass.length)];
    success({
      type: "normal",
      class: clazz as TerminalMessageClass,
      tag: clazz,
      content: `Your command is '${command}'`,
    });
  }
};
</script>
<template>
  <div id="app">
    <terminal name="my-terminal" @exec-cmd="onExecCmd"></terminal>
  </div>
</template>

<style>
body,
html,
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}
</style>
