<template>
  <div class="single-chart-container">
    <div
      ref="chartRef"
      class="chart"
      :style="{ width: chartWidth, height: chartHeight }"
    ></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { metricsConfig } from "@/config/charts_metrics.js";

const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [],
  },
  // 图表宽度
  chartWidth: {
    type: String,
    default: "100%",
  },
  // 图表高度
  chartHeight: {
    type: String,
    default: "300px",
  },
  // 指标名称
  metric: {
    type: String,
    required: true,
  },
  // 图表标题
  title: {
    type: String,
    default: "",
  },
});

const chartRef = ref(null);
let chartInstance = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

// 更新图表
const updateChart = () => {
  if (!chartInstance || !props.data.length) return;

  const config = metricsConfig[props.metric];
  if (!config) return;

  const timeData = props.data.map((item) => item.time);
  const values = props.data.map((item) => item[props.metric]);

  const option = {
    title: {
      text: props.title || `${config.name} (${config.unit})`,
      left: "center",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#333",
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "#333",
      textStyle: {
        color: "#fff",
      },
      formatter: (params) => {
        const param = params[0];
        return `时间: ${param.axisValue} 天<br/>${config.name}: ${param.value} ${config.unit}`;
      },
    },
    grid: {
      left: "12%",
      right: "8%",
      top: "20%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      name: "时间 (天)",
      nameLocation: "middle",
      nameGap: 25,
      data: timeData,
      axisLine: {
        lineStyle: {
          color: "#666",
        },
      },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    yAxis: {
      type: "value",
      name: `${config.name} (${config.unit})`,
      nameLocation: "middle",
      nameGap: 50,
      nameTextStyle: {
        color: config.color,
        fontSize: 12,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: config.color,
        },
      },
      axisLabel: {
        color: config.color,
        fontSize: 12,
        formatter: (value) => {
          // 根据指标类型格式化数值
          if (props.metric === "Volume") {
            return (value / 1000000).toFixed(1) + "M";
          }
          if (props.metric === "FGPR") {
            return (value / 1000).toFixed(1) + "K";
          }
          if (props.metric === "FWPR") {
            return value.toFixed(4);
          }
          return value.toFixed(1);
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#e8e8e8",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: config.name,
        type: "line",
        data: values,
        lineStyle: {
          color: config.color,
          width: 3,
        },
        itemStyle: {
          color: config.color,
        },
        symbol: "circle",
        symbolSize: 6,
        smooth: true,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: config.color + "40", // 透明度40%
              },
              {
                offset: 1,
                color: config.color + "10", // 透明度10%
              },
            ],
          },
        },
      },
    ],
    dataZoom: [
      {
        type: "slider",
        start: 0,
        end: 100,
        height: 20,
        bottom: 5,
      },
    ],
  };

  chartInstance.setOption(option, true);
};

// 响应式调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleResize);
});

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart();
  },
  { deep: true }
);

watch(
  () => props.metric,
  () => {
    updateChart();
  }
);
</script>

<style lang="scss" scoped>
.single-chart-container {
  width: 100%;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart {
  width: 100%;
  min-height: 300px;
}
</style>
