<template>
  <div class="workflow-workspace">
    <div class="workspace-container">
      <!-- 左侧模型列表 -->
      <div class="model-panel">
        <ModelList @drag-start="handleDragStart" />
      </div>

      <!-- 右侧工作区 -->
      <div class="canvas-panel">
        <WorkflowCanvas ref="canvasRef" @node-settings="handleNodeSettings" />
      </div>
    </div>

    <!-- 节点设置菜单 -->
    <NodeSettingsMenu
      v-if="showSettingsMenu"
      :visible="showSettingsMenu"
      :position="menuPosition"
      :node-data="selectedNode"
      @close="handleMenuClose"
      @action="handleMenuAction"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import ModelList from "@/components/workFlow/ModelList.vue";
import WorkflowCanvas from "@/components/workFlow/WorkflowCanvas.vue";
import NodeSettingsMenu from "@/components/workFlow/NodeSettingsMenu.vue";

// 响应式数据
const canvasRef = ref(null);
const showSettingsMenu = ref(false);
const menuPosition = ref({ x: 0, y: 0 });
const selectedNode = ref(null);

// 处理拖拽开始
const handleDragStart = (modelData) => {
  console.log("开始拖拽模型:", modelData);
};

// 处理节点设置按钮点击
const handleNodeSettings = (nodeData, position) => {
  selectedNode.value = nodeData;
  menuPosition.value = position;
  showSettingsMenu.value = true;
};

// 处理菜单关闭
const handleMenuClose = () => {
  showSettingsMenu.value = false;
  selectedNode.value = null;
};

// 处理菜单操作
const handleMenuAction = (action, nodeData) => {
  console.log("菜单操作:", action, nodeData);

  switch (action) {
    case "edit":
      // 编辑节点
      console.log("编辑节点:", nodeData);
      break;
    case "delete":
      // 删除节点
      if (canvasRef.value) {
        canvasRef.value.deleteNode(nodeData.id);
      }
      break;
    case "copy":
      // 复制节点
      if (canvasRef.value) {
        canvasRef.value.copyNode(nodeData.id);
      }
      break;
  }

  handleMenuClose();
};

onMounted(() => {
  console.log("工作流工作区已挂载");
});
</script>

<style lang="scss" scoped>
.workflow-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.workspace-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.model-panel {
  width: 300px;
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.canvas-panel {
  flex: 1;
  position: relative;
  background-color: #fafafa;
}
</style>
