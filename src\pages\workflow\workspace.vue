<template>
  <div class="workflow-workspace">
    <!-- 顶部工具栏 -->
    <div class="workspace-header">
      <div class="header-left">
        <h2 class="workspace-title">工作流设计器</h2>
        <div class="workspace-info">
          <span class="project-name">{{ currentProject.name }}</span>
          <el-tag
            size="small"
            :type="currentProject.status === 'saved' ? 'success' : 'warning'"
          >
            {{ currentProject.status === "saved" ? "已保存" : "未保存" }}
          </el-tag>
        </div>
      </div>

      <div class="header-actions">
        <el-button-group>
          <el-button size="small" @click="handleSave" :disabled="!hasChanges">
            <el-icon><DocumentCopy /></el-icon>
            保存
          </el-button>
          <el-button size="small" @click="handleUndo">
            <el-icon><RefreshLeft /></el-icon>
            撤销
          </el-button>
          <el-button size="small" @click="handleRedo">
            <el-icon><RefreshRight /></el-icon>
            重做
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button size="small" @click="handleZoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button size="small" @click="handleZoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small" @click="handleFitToContent">
            <el-icon><FullScreen /></el-icon>
            适应画布
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button type="primary" size="small" @click="handleRunWorkflow">
          <el-icon><VideoPlay /></el-icon>
          运行工作流
        </el-button>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="workspace-content">
      <!-- 左侧节点面板 -->
      <div class="left-panel">
        <WorkflowStencil @node-drag-start="handleNodeDragStart" />
      </div>

      <!-- 中间画布区域 -->
      <div class="center-panel">
        <WorkflowCanvas
          ref="canvasRef"
          @node-click="handleNodeClick"
          @edge-click="handleEdgeClick"
          @canvas-click="handleCanvasClick"
          @graph-ready="handleGraphReady"
          @node-run-state-changed="handleNodeRunStateChanged"
          @node-expand-state-changed="handleNodeExpandStateChanged"
          @show-node-menu="handleShowNodeMenu"
          @edit-node-properties="handleEditNodeProperties"
          @view-node-logs="handleViewNodeLogs"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel">
        <WorkflowProperties
          :selected-element="selectedElement"
          @property-change="handlePropertyChange"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="workspace-footer">
      <div class="footer-left">
        <span class="node-count">节点: {{ nodeCount }}</span>
        <span class="edge-count">连线: {{ edgeCount }}</span>
      </div>

      <div class="footer-center">
        <span class="zoom-info">缩放: {{ Math.round(zoomLevel * 100) }}%</span>
      </div>

      <div class="footer-right">
        <span class="coordinates" v-if="mousePosition.x !== null">
          坐标: ({{ mousePosition.x }}, {{ mousePosition.y }})
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import {
  DocumentCopy,
  RefreshLeft,
  RefreshRight,
  ZoomIn,
  ZoomOut,
  FullScreen,
  VideoPlay,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import WorkflowStencil from "@/components/workFlow/WorkflowStencil.vue";
import WorkflowCanvas from "@/components/workFlow/WorkflowCanvas.vue";
import WorkflowProperties from "@/components/workFlow/WorkflowProperties.vue";

// 响应式数据
const canvasRef = ref(null);
const selectedElement = ref(null);
const hasChanges = ref(false);
const nodeCount = ref(0);
const edgeCount = ref(0);
const zoomLevel = ref(1);

const currentProject = reactive({
  name: "新建工作流",
  status: "unsaved",
});

const mousePosition = reactive({
  x: null,
  y: null,
});

// 计算属性
const graphInstance = computed(() => {
  return canvasRef.value?.getGraph();
});

// 事件处理方法
const handleSave = () => {
  currentProject.status = "saved";
  hasChanges.value = false;
  ElMessage.success("工作流已保存");
};

const handleUndo = () => {
  if (graphInstance.value) {
    graphInstance.value.undo();
    ElMessage.info("已撤销");
  }
};

const handleRedo = () => {
  if (graphInstance.value) {
    graphInstance.value.redo();
    ElMessage.info("已重做");
  }
};

const handleZoomIn = () => {
  if (graphInstance.value) {
    graphInstance.value.zoom(0.1);
    zoomLevel.value = graphInstance.value.zoom();
  }
};

const handleZoomOut = () => {
  if (graphInstance.value) {
    graphInstance.value.zoom(-0.1);
    zoomLevel.value = graphInstance.value.zoom();
  }
};

const handleFitToContent = () => {
  if (graphInstance.value) {
    graphInstance.value.zoomToFit({ padding: 20 });
    zoomLevel.value = graphInstance.value.zoom();
  }
};

const handleRunWorkflow = () => {
  ElMessage.success("开始运行工作流");
  // 这里可以添加工作流执行逻辑
};

const handleNodeDragStart = (nodeData) => {
  console.log("节点拖拽开始:", nodeData);
};

const handleNodeClick = (nodeData) => {
  selectedElement.value = {
    type: "node",
    data: nodeData,
  };
  console.log("节点被选中:", nodeData);
};

const handleEdgeClick = (edgeData) => {
  selectedElement.value = {
    type: "edge",
    data: edgeData,
  };
  console.log("连线被选中:", edgeData);
};

const handleCanvasClick = () => {
  selectedElement.value = null;
  console.log("画布被点击");
};

const handleGraphReady = (graph) => {
  graphInstance.value = graph;
  console.log("画布准备就绪:", graph);

  // 监听图形变化
  graph.on("cell:added", () => {
    updateStats();
    markAsChanged();
  });

  graph.on("cell:removed", () => {
    updateStats();
    markAsChanged();
  });

  graph.on("scale", () => {
    zoomLevel.value = graph.zoom();
  });
};

const handlePropertyChange = (property, value) => {
  console.log("属性变更:", property, value);

  if (!selectedElement.value || !graphInstance.value) return;

  const elementType = selectedElement.value.type;
  const elementId = selectedElement.value.data.id;

  if (elementType === "node") {
    const node = graphInstance.value.getCellById(elementId);
    if (!node) return;

    switch (property) {
      case "name":
        // 更新节点标签
        node.setAttrByPath("text/text", value);
        // 更新数据
        node.setData({ ...node.getData(), name: value });
        break;

      case "description":
        // 更新数据中的描述
        node.setData({ ...node.getData(), description: value });
        break;

      case "position":
        // 更新节点位置
        node.setPosition(value.x, value.y);
        break;

      case "size":
        // 更新节点大小
        node.setSize(value.width, value.height);
        break;

      case "style":
        // 更新样式属性
        if (value.backgroundColor) {
          node.setAttrByPath("body/fill", value.backgroundColor);
        }
        if (value.borderColor) {
          node.setAttrByPath("body/stroke", value.borderColor);
        }
        if (value.textColor) {
          node.setAttrByPath("text/fill", value.textColor);
        }
        if (value.borderWidth) {
          node.setAttrByPath("body/strokeWidth", value.borderWidth);
        }
        break;
      case "customProperty":
        // 更新自定义属性
        const currentData = node.getData();
        const properties = { ...currentData.properties };
        properties[value.key] = value.value;
        node.setData({ ...currentData, properties });

        // 根据特定属性动态更新节点样式
        updateNodeStyleBasedOnProperties(node, properties);
        break;

      case "port":
        // 更新端口配置
        const nodeData = node.getData();
        if (nodeData.ports && nodeData.ports[value.portIndex]) {
          nodeData.ports[value.portIndex][value.property] = value.value;
          node.setData({ ...nodeData });
        }
        break;
    }

    // 更新选中元素的数据，保持属性面板同步
    selectedElement.value.data = {
      id: node.id,
      shape: node.shape,
      data: node.getData(),
      position: node.position(),
      size: node.size(),
    };
  } else if (elementType === "edge") {
    const edge = graphInstance.value.getCellById(elementId);
    if (!edge) return;

    switch (property) {
      case "style":
        if (value.strokeColor) {
          edge.setAttrByPath("line/stroke", value.strokeColor);
        }
        if (value.strokeWidth) {
          edge.setAttrByPath("line/strokeWidth", value.strokeWidth);
        }
        if (value.strokeDasharray && value.strokeDasharray !== "none") {
          edge.setAttrByPath("line/strokeDasharray", value.strokeDasharray);
        } else {
          edge.removeAttrByPath("line/strokeDasharray");
        }
        break;
    }

    // 更新选中元素的数据
    selectedElement.value.data = {
      id: edge.id,
      source: edge.getSource(),
      target: edge.getTarget(),
      data: edge.getData(),
    };
  }
  markAsChanged();
};

// 根据属性动态更新节点样式
const updateNodeStyleBasedOnProperties = (node, properties) => {
  if (!node || !properties) return;

  const nodeData = node.getData();
  const nodeType = nodeData.type;

  // 文件上传节点的动态样式
  if (nodeData.id === "file-upload" || nodeType === "input") {
    // 如果有文件类型，改变边框样式
    if (properties.fileType) {
      const fileTypeColors = {
        csv: "#67C23A",
        json: "#409EFF",
        xml: "#E6A23C",
        txt: "#909399",
        excel: "#52C41A",
        pdf: "#F56C6C",
      };

      const color =
        fileTypeColors[properties.fileType.toLowerCase()] || "#67C23A";
      node.setAttrByPath("body/stroke", color);

      // 如果文件类型是危险类型，添加警告样式
      if (["exe", "bat", "sh"].includes(properties.fileType.toLowerCase())) {
        node.setAttrByPath("body/strokeWidth", 3);
        node.setAttrByPath("body/strokeDasharray", "5,5");
      } else {
        node.setAttrByPath("body/strokeWidth", 2);
        node.removeAttrByPath("body/strokeDasharray");
      }
    }

    // 根据最大文件大小调整节点透明度
    if (properties.maxSize) {
      const sizeValue = parseFloat(properties.maxSize);
      if (sizeValue > 100) {
        // 超过100MB
        node.setAttrByPath("body/fillOpacity", 0.8);
      } else {
        node.setAttrByPath("body/fillOpacity", 1);
      }
    }
  }

  // 数据分析节点的动态样式
  if (nodeData.id === "data-analysis" || nodeType === "processing") {
    // 根据分析类型改变图标颜色
    if (properties.analysisType) {
      const analysisColors = {
        statistics: "#1A59B0",
        correlation: "#722ED1",
        trend: "#13C2C2",
      };

      const color = analysisColors[properties.analysisType] || "#1A59B0";
      node.setAttrByPath("body/fill", color);
    }
  }

  // 定时触发节点的动态样式
  if (nodeData.id === "timer-trigger") {
    // 根据时间间隔改变节点脉动效果
    if (properties.interval) {
      const intervalValue = properties.interval;
      if (intervalValue.includes("s")) {
        // 秒级触发
        node.setAttrByPath("body/stroke", "#F56C6C"); // 红色表示高频
      } else if (intervalValue.includes("m")) {
        // 分钟级
        node.setAttrByPath("body/stroke", "#E6A23C"); // 橙色表示中频
      } else {
        // 小时级或更长
        node.setAttrByPath("body/stroke", "#67C23A"); // 绿色表示低频
      }
    }
  }

  // 通用属性：如果有错误状态，显示错误样式
  if (properties.hasError) {
    node.setAttrByPath("body/stroke", "#F56C6C");
    node.setAttrByPath("body/strokeWidth", 3);
  }

  // 通用属性：如果节点被禁用，显示禁用样式
  if (properties.disabled) {
    node.setAttrByPath("body/fillOpacity", 0.5);
    node.setAttrByPath("text/fillOpacity", 0.5);
  } else {
    node.setAttrByPath("body/fillOpacity", 1);
    node.setAttrByPath("text/fillOpacity", 1);
  }

  console.log("节点样式已更新:", nodeData.name, properties);
};

const updateStats = () => {
  if (graphInstance.value) {
    const cells = graphInstance.value.getCells();
    nodeCount.value = cells.filter((cell) => cell.isNode()).length;
    edgeCount.value = cells.filter((cell) => cell.isEdge()).length;
  }
};

const markAsChanged = () => {
  hasChanges.value = true;
  currentProject.status = "unsaved";
};

// 新增的事件处理函数
const handleNodeRunStateChanged = ({ cell, isRunning }) => {
  console.log(
    `节点 ${cell.getLabel()} 运行状态变为: ${isRunning ? "运行中" : "已停止"}`
  );
  markAsChanged();

  // 可以在这里添加具体的节点运行逻辑
  if (isRunning) {
    ElMessage.success(`节点 ${cell.getLabel()} 开始运行`);
  } else {
    ElMessage.info(`节点 ${cell.getLabel()} 已停止运行`);
  }
};

const handleNodeExpandStateChanged = ({ cell, isExpanded }) => {
  console.log(
    `节点 ${cell.getLabel()} 展开状态变为: ${isExpanded ? "展开" : "收起"}`
  );
  markAsChanged();

  // 可以在这里添加节点展开/收起的具体逻辑
  // 比如显示/隐藏节点的详细信息面板
};

const handleShowNodeMenu = ({ cell, nodeData, menuItems, position }) => {
  console.log("显示节点菜单:", nodeData.name || cell.getLabel());

  // 这里可以显示一个上下文菜单
  // 由于这是一个演示，我们只在控制台显示菜单项
  console.log(
    "菜单选项:",
    menuItems.map((item) => item.label)
  );

  // 实际项目中可以使用 ElDropdown 或者自定义的上下文菜单组件
  ElMessage.info(`节点 ${nodeData.name || cell.getLabel()} 的操作菜单已显示`);
};

const handleEditNodeProperties = (cell) => {
  console.log("编辑节点属性:", cell.getLabel());

  // 选中节点并显示在属性面板中
  selectedElement.value = {
    type: "node",
    data: cell,
  };

  ElMessage.info(`正在编辑节点 ${cell.getLabel()} 的属性`);
};

const handleViewNodeLogs = (cell) => {
  console.log("查看节点日志:", cell.getLabel());

  // 这里可以打开一个日志查看器
  ElMessage.info(`查看节点 ${cell.getLabel()} 的运行日志`);

  // 实际项目中可以打开一个对话框显示日志内容
};

// 生命周期
onMounted(() => {
  console.log("工作流工作台已挂载");
});
</script>

<style lang="scss" scoped>
.workflow-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.workspace-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .workspace-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .workspace-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .project-name {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-divider {
      margin: 0;
      height: 20px;
    }
  }
}

.workspace-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.left-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  overflow: hidden;
}

.center-panel {
  flex: 1;
  background: #fafbfc;
  position: relative;
  overflow: hidden;
}

.right-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e4e7ed;
  overflow: hidden;
}

.workspace-footer {
  height: 32px;
  background: white;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 12px;
  color: #909399;

  .footer-left,
  .footer-center,
  .footer-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .footer-center {
    justify-content: center;
  }

  .footer-right {
    justify-content: flex-end;
  }
}

@media (max-width: 1200px) {
  .left-panel {
    width: 260px;
  }

  .right-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .workspace-header {
    padding: 0 12px;

    .header-actions {
      gap: 8px;
    }
  }

  .workspace-footer {
    padding: 0 12px;
    font-size: 11px;
  }
}
</style>
