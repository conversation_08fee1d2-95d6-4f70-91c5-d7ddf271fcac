import axios from "axios";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";

// 创建 axios 实例
const request = axios.create({
  baseURL: "/", // 基础URL，可通过环境变量配置
  timeout: 10000, // 请求超时时间 10秒
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么

    // 获取token并检查是否过期
    const token = localStorage.getItem("token") || "";
    if (token) {
      // token有效，添加到请求头
      config.headers.Authorization = `${token}`;
    }

    // 如果是 FormData，让浏览器自动设置 Content-Type
    if (config.data instanceof FormData) {
      delete config.headers["Content-Type"];
    }

    // 添加时间戳防止缓存
    if (config.method === "get") {
      config.params = {
        ...config.params,
      };
    }

    return config;
  },
  (error) => {
    // 对请求错误做些什么
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    const { data, status, config } = response;

    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (status === 200) {
      // 特殊处理：文件内容接口返回纯文本，直接返回数据
      if (config.url && config.url.includes("/files/content")) {
        return data;
      }

      if (data.code === 800101) {
        ElMessage.error(data.message || "登录已过期，请重新登录");
        // 清除token并跳转到登录页
        localStorage.removeItem("token");
        setTimeout(() => {
          window.location.href = "/oilgas/#/login";
        }, 1000);
        return Promise.reject(new Error(data.message || "登录已过期"));
      }

      // 根据后端返回的数据结构进行判断
      if (data && typeof data === "object" && data.code === 0) {
        return data;
      } else if (data && typeof data === "object" && data.code !== undefined) {
        // 业务错误处理
        ElMessage.error(data.message || "请求失败");
        return Promise.reject(new Error(data.message || "请求失败"));
      } else {
        // 对于非JSON格式的响应（如纯文本），直接返回
        return data;
      }
    } else {
      ElMessage.error("网络异常");
      return Promise.reject(new Error("网络异常"));
    }
  },
  (error) => {
    // 对响应错误做点什么
    console.error("响应错误:", error);

    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          removeToken();
          ElMessage.error("登录已过期，请重新登录");
          // 跳转到登录页
          setTimeout(() => {
            window.location.href = "/login";
          }, 1000);
          break;
        case 403:
          ElMessage.error("没有权限访问");
          break;
        case 404:
          ElMessage.error("请求的资源不存在");
          break;
        case 500:
          ElMessage.error("服务器内部错误");
          break;
        default:
          ElMessage.error(data?.message || "请求失败");
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      ElMessage.error("网络连接异常，请检查网络");
    } else {
      // 其他错误
      ElMessage.error(error.message || "请求失败");
    }

    return Promise.reject(error);
  }
);

/**
 * 封装的请求方法
 */
const http = {
  /**
   * GET 请求
   * @param {string} url 请求地址
   * @param {object} params 请求参数
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  get(url, params = {}, config = {}) {
    return request({
      method: "get",
      url,
      params,
      ...config,
    });
  },

  /**
   * POST 请求
   * @param {string} url 请求地址
   * @param {object} data 请求数据
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  post(url, data = {}, config = {}) {
    return request({
      method: "post",
      url,
      data,
      ...config,
    });
  },

  /**
   * PUT 请求
   * @param {string} url 请求地址
   * @param {object} data 请求数据
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  put(url, data = {}, config = {}) {
    return request({
      method: "put",
      url,
      data,
      ...config,
    });
  },
  /**
   * DELETE 请求
   * @param {string} url 请求地址
   * @param {object} params 请求数据（可选）
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  delete(url, params = {}, config = {}) {
    return request({
      method: "DELETE",
      url,
      params,
      ...config,
    });
  },

  /**
   * PATCH 请求
   * @param {string} url 请求地址
   * @param {object} data 请求数据
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  patch(url, data = {}, config = {}) {
    return request({
      method: "patch",
      url,
      data,
      ...config,
    });
  },

  /**
   * 文件上传
   * @param {string} url 上传地址
   * @param {FormData} formData 表单数据
   * @param {object} config 请求配置
   * @returns {Promise}
   */
  upload(url, formData, config = {}) {
    return request({
      method: "post",
      url,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      ...config,
    });
  },
  /**
   * 文件下载
   * @param {string} url 下载地址
   * @param {object} params 请求参数
   * @param {string} filename 文件名
   * @returns {Promise}
   */
  download(url, params = {}, filename = "download") {
    return request({
      method: "post",
      url,
      params,
      responseType: "blob",
    }).then((response) => {
      const blob = new Blob([response]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    });
  },
};

// 导出 axios 实例和封装的方法
export { request };
export default http;
