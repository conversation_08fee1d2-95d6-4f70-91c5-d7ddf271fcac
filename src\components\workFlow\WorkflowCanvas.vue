<template>
  <div class="workflow-canvas-container">
    <div ref="container" class="workflow-canvas"></div>
  </div>
</template>

<script>
import { Graph, Shape, Edge, Cell } from "@antv/x6";
import "@antv/x6-vue-shape";
import { Stencil } from "@antv/x6-plugin-stencil";
import { History } from "@antv/x6-plugin-history";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { Selection } from "@antv/x6-plugin-selection";
import { handleDrop } from "./handleDrop";

export default {
  name: "WorkflowCanvas",
  data() {
    return {
      graph: null,
      stencil: null,
      nodeId: 1,
      resizeTimer: null, // 用于防抖处理
    };
  },
  mounted() {
    // 使用 nextTick 确保 DOM 已完全渲染
    this.$nextTick(() => {
      this.initGraph();
      this.registerNodeShapes();
      this.setupEventListeners();
    });
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleResize);
  },
  beforeUnmount() {
    if (this.graph) {
      this.graph.dispose();
    }
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleResize);
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer);
    } // 清理拖拽事件监听器
    const container = this.$refs.container;
    if (container) {
      container.removeEventListener("dragover", this.handleDragOver);
      container.removeEventListener("drop", this.handleDropEvent);
    }
  },
  methods: {
    // 初始化画布
    initGraph() {
      // 获取容器尺寸
      const container = this.$refs.container;
      const { clientWidth, clientHeight } = container;

      this.graph = new Graph({
        container: container,
        width: clientWidth,
        height: clientHeight,
        grid: {
          visible: true,
          type: "doubleMesh",
          args: [
            {
              color: "#eee", // 主网格线颜色
              thickness: 1, // 主网格线宽度
            },
            {
              color: "#ddd", // 次网格线颜色
              thickness: 1, // 次网格线宽度
              factor: 4, // 主次网格线间隔
            },
          ],
        },
        panning: {
          enabled: true,
          eventTypes: ["leftMouseDown", "mouseWheel"],
        },
        mousewheel: {
          enabled: true,
          modifiers: "ctrl",
          factor: 1.1,
          maxScale: 3,
          minScale: 0.3,
        },
        highlighting: {
          magnetAdsorbed: {
            name: "stroke",
            args: {
              attrs: {
                fill: "#5F95FF",
                stroke: "#5F95FF",
              },
            },
          },
        },
        resizing: true,
        rotating: true,
        selecting: {
          enabled: true,
          rubberband: true,
          showNodeSelectionBox: true,
        },
        snapline: true,
        keyboard: true,
        clipboard: true,
      });

      // 启用插件
      this.graph
        .use(new History())
        .use(new Snapline())
        .use(new Keyboard())
        .use(new Clipboard())
        .use(new Selection());
    }, // 处理窗口大小变化
    handleResize() {
      // 防抖处理，避免频繁触发
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.graph) {
          const container = this.$refs.container;
          const { clientWidth, clientHeight } = container;
          this.graph.resize(clientWidth, clientHeight);
        }
      }, 100); // 100ms 防抖延迟
    },

    // 注册节点形状 - 改用SVG rect节点以支持按钮工具
    registerNodeShapes() {
      // 注册基础工作流节点
      Graph.registerNode("workflow-node", {
        inherit: "rect",
        width: 160,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            rx: 8,
            ry: 8,
          },
          label: {
            fontSize: 14,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: "top",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            right: {
              position: "right",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            bottom: {
              position: "bottom",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            left: {
              position: "left",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
          },
          items: [
            {
              group: "top",
            },
            {
              group: "right",
            },
            {
              group: "bottom",
            },
            {
              group: "left",
            },
          ],
        },
      });

      // 注册开始节点
      Graph.registerNode("start-node", {
        inherit: "workflow-node",
        attrs: {
          body: {
            stroke: "#52C41A",
            fill: "#F6FFED",
          },
        },
      });

      // 注册结束节点
      Graph.registerNode("end-node", {
        inherit: "workflow-node",
        attrs: {
          body: {
            stroke: "#FF4D4F",
            fill: "#FFF2F0",
          },
        },
      });

      // 注册决策节点
      Graph.registerNode("decision-node", {
        inherit: "polygon",
        width: 120,
        height: 80,
        attrs: {
          body: {
            refPoints: "0,10 10,0 20,10 10,20",
            fill: "#FFE7BA",
            stroke: "#FAAD14",
            strokeWidth: 2,
          },
          label: {
            fontSize: 14,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: { name: "absolute", args: { x: 60, y: 0 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            right: {
              position: { name: "absolute", args: { x: 120, y: 40 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            bottom: {
              position: { name: "absolute", args: { x: 60, y: 80 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            left: {
              position: { name: "absolute", args: { x: 0, y: 40 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
          },
          items: [
            { group: "top" },
            { group: "right" },
            { group: "bottom" },
            { group: "left" },
          ],
        },
      });
    },

    // 创建运行/暂停按钮
    createRunButton(data) {
      const isRunning = data.isRunning || false;
      return {
        name: "button",
        args: {
          markup: `
            <circle r="8" stroke="#67C23A" stroke-width="2" fill="${
              isRunning ? "#67C23A" : "#fff"
            }" cursor="pointer"/>
            <text fill="${
              isRunning ? "#fff" : "#67C23A"
            }" font-size="10" text-anchor="middle" dy="3" cursor="pointer">
              ${isRunning ? "⏸" : "▶"}
            </text>
          `,
          x: -15,
          y: 15,
          onClick: ({ cell }) => {
            const nodeData = cell.getData();
            const newRunningState = !nodeData.isRunning;

            // 更新节点数据
            cell.setData({
              ...nodeData,
              isRunning: newRunningState,
            });

            // 重新创建按钮以更新状态
            cell.removeTools();
            cell.addTool(
              this.createRunButton({ ...nodeData, isRunning: newRunningState })
            );
            cell.addTool(this.createExpandButton(nodeData));
            cell.addTool(this.createMenuButton(nodeData));

            console.log(
              `节点 ${nodeData.name || "未命名"} ${
                newRunningState ? "开始运行" : "暂停运行"
              }`
            );
            this.$emit("node-run-state-changed", {
              cell,
              isRunning: newRunningState,
            });
          },
        },
      };
    },

    // 创建展开/收起按钮
    createExpandButton(data) {
      const isExpanded = data.isExpanded || false;
      return {
        name: "button",
        args: {
          markup: `
            <circle r="8" stroke="#409EFF" stroke-width="2" fill="${
              isExpanded ? "#409EFF" : "#fff"
            }" cursor="pointer"/>
            <text fill="${
              isExpanded ? "#fff" : "#409EFF"
            }" font-size="12" text-anchor="middle" dy="4" cursor="pointer">
              ${isExpanded ? "−" : "+"}
            </text>
          `,
          x: -15,
          y: -15,
          onClick: ({ cell }) => {
            const nodeData = cell.getData();
            const newExpandedState = !nodeData.isExpanded;

            // 更新节点数据
            cell.setData({
              ...nodeData,
              isExpanded: newExpandedState,
            });

            // 重新创建按钮以更新状态
            cell.removeTools();
            cell.addTool(this.createRunButton(nodeData));
            cell.addTool(
              this.createExpandButton({
                ...nodeData,
                isExpanded: newExpandedState,
              })
            );
            cell.addTool(this.createMenuButton(nodeData));

            console.log(
              `节点 ${nodeData.name || "未命名"} ${
                newExpandedState ? "展开" : "收起"
              }`
            );
            this.$emit("node-expand-state-changed", {
              cell,
              isExpanded: newExpandedState,
            });
          },
        },
      };
    },

    // 创建汉堡菜单按钮
    createMenuButton(data) {
      return {
        name: "button",
        args: {
          markup: `
            <circle r="8" stroke="#E6A23C" stroke-width="2" fill="#fff" cursor="pointer"/>
            <g fill="#E6A23C" cursor="pointer">
              <rect x="-4" y="-2" width="8" height="1"/>
              <rect x="-4" y="0" width="8" height="1"/>
              <rect x="-4" y="2" width="8" height="1"/>
            </g>
          `,
          x: 15,
          y: 15,
          onClick: ({ cell, e }) => {
            e.stopPropagation();
            this.showNodeMenu(cell, e);
          },
        },
      };
    },

    // 显示节点菜单
    showNodeMenu(cell, event) {
      const nodeData = cell.getData();
      const menuItems = [
        {
          label: "编辑属性",
          icon: "⚙️",
          action: () => this.editNodeProperties(cell),
        },
        {
          label: "复制节点",
          icon: "📋",
          action: () => this.copyNode(cell),
        },
        {
          label: "删除节点",
          icon: "🗑️",
          action: () => this.deleteNode(cell),
        },
        {
          label: "查看日志",
          icon: "📄",
          action: () => this.viewNodeLogs(cell),
        },
        {
          label: "重置状态",
          icon: "🔄",
          action: () => this.resetNodeState(cell),
        },
      ];

      // 触发菜单显示事件
      this.$emit("show-node-menu", {
        cell,
        nodeData,
        menuItems,
        position: { x: event.clientX, y: event.clientY },
      });
    },

    // 编辑节点属性
    editNodeProperties(cell) {
      console.log("编辑节点属性:", cell.getLabel());
      this.$emit("edit-node-properties", cell);
    },

    // 复制节点
    copyNode(cell) {
      const position = cell.getPosition();
      const size = cell.getSize();
      const nodeData = cell.getData();

      // 创建新节点，位置稍微偏移
      const newNode = this.graph.addNode({
        shape: cell.shape,
        x: position.x + 30,
        y: position.y + 30,
        width: size.width,
        height: size.height,
        label: `${cell.getLabel()}_副本`,
        data: {
          ...nodeData,
          nodeId: `node_${Date.now()}_${Math.random()
            .toString(36)
            .substr(2, 9)}`,
          createdAt: new Date().toISOString(),
        },
      });

      // 选中新节点
      this.graph.select(newNode);
      console.log("节点已复制");
    },

    // 删除节点
    deleteNode(cell) {
      if (confirm(`确定要删除节点 "${cell.getLabel()}" 吗？`)) {
        this.graph.removeNode(cell);
        console.log("节点已删除");
      }
    },

    // 查看节点日志
    viewNodeLogs(cell) {
      console.log("查看节点日志:", cell.getLabel());
      this.$emit("view-node-logs", cell);
    },

    // 重置节点状态
    resetNodeState(cell) {
      const nodeData = cell.getData();
      cell.setData({
        ...nodeData,
        isRunning: false,
        isExpanded: false,
      });

      // 重新创建按钮
      cell.removeTools();
      console.log("节点状态已重置");
    },

    // 处理窗口大小变化
    handleResize() {
      // 防抖处理，避免频繁触发
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.graph) {
          const container = this.$refs.container;
          const { clientWidth, clientHeight } = container;
          this.graph.resize(clientWidth, clientHeight);
        }
      }, 100); // 100ms 防抖延迟
    },

    // 注册节点形状 - 改用SVG rect节点以支持按钮工具
    registerNodeShapes() {
      // 注册基础工作流节点
      Graph.registerNode("workflow-node", {
        inherit: "rect",
        width: 160,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            rx: 8,
            ry: 8,
          },
          label: {
            fontSize: 14,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: "top",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            right: {
              position: "right",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            bottom: {
              position: "bottom",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            left: {
              position: "left",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
          },
          items: [
            {
              group: "top",
            },
            {
              group: "right",
            },
            {
              group: "bottom",
            },
            {
              group: "left",
            },
          ],
        },
      });

      // 注册开始节点
      Graph.registerNode("start-node", {
        inherit: "workflow-node",
        attrs: {
          body: {
            stroke: "#52C41A",
            fill: "#F6FFED",
          },
        },
      });

      // 注册结束节点
      Graph.registerNode("end-node", {
        inherit: "workflow-node",
        attrs: {
          body: {
            stroke: "#FF4D4F",
            fill: "#FFF2F0",
          },
        },
      });

      // 注册决策节点
      Graph.registerNode("decision-node", {
        inherit: "polygon",
        width: 120,
        height: 80,
        attrs: {
          body: {
            refPoints: "0,10 10,0 20,10 10,20",
            fill: "#FFE7BA",
            stroke: "#FAAD14",
            strokeWidth: 2,
          },
          label: {
            fontSize: 14,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: { name: "absolute", args: { x: 60, y: 0 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            right: {
              position: { name: "absolute", args: { x: 120, y: 40 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            bottom: {
              position: { name: "absolute", args: { x: 60, y: 80 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            left: {
              position: { name: "absolute", args: { x: 0, y: 40 } },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#FAAD14",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
          },
          items: [
            { group: "top" },
            { group: "right" },
            { group: "bottom" },
            { group: "left" },
          ],
        },
      });
    },

    // 创建节点的公共方法
    createNode(type, label, position) {
      const nodeShapes = {
        start: "start-node",
        process: "workflow-node",
        decision: "decision-node",
        end: "end-node",
      };

      const node = this.graph.addNode({
        shape: nodeShapes[type] || "workflow-node",
        x: position.x,
        y: position.y,
        label: label,
        data: {
          type: type,
          isRunning: false,
          isExpanded: false,
        },
      });

      return node;
    },

    // 获取画布实例
    getGraph() {
      return this.graph;
    },

    // 居中画布内容
    centerContent() {
      this.graph.centerContent();
    },

    // 缩放到合适大小
    zoomToFit() {
      this.graph.zoomToFit({ padding: 20 });
    },

    // 导出画布数据
    exportData() {
      return this.graph.toJSON();
    },

    // 导入画布数据
    importData(data) {
      this.graph.fromJSON(data);
    },

    // 清空画布
    clearCanvas() {
      this.graph.clearCells();
    },

    // 设置事件监听器
    setupEventListeners() {
      // 设置画布容器的拖拽事件
      const container = this.$refs.container;

      // 允许拖拽放置
      container.addEventListener("dragover", this.handleDragOver);

      // 处理拖拽放置
      container.addEventListener("drop", this.handleDropEvent);

      // 鼠标进入节点时显示按钮
      this.graph.on("node:mouseenter", ({ cell }) => {
        if (cell.shape?.includes("node")) {
          const data = cell.getData() || {};

          // 添加运行按钮
          cell.addTool(this.createRunButton(data));

          // 添加展开按钮
          cell.addTool(this.createExpandButton(data));

          // 添加汉堡菜单按钮
          cell.addTool(this.createMenuButton(data));
        }
      });

      // 鼠标离开节点时隐藏按钮
      this.graph.on("node:mouseleave", ({ cell }) => {
        if (cell.shape?.includes("node")) {
          // 移除按钮
          cell.removeTools();
        }
      });

      // 监听节点双击事件
      this.graph.on("node:dblclick", ({ cell }) => {
        console.log("双击节点:", cell.getLabel());
        this.$emit("node-double-click", cell);
      });

      // 监听边双击事件
      this.graph.on("edge:dblclick", ({ cell }) => {
        console.log("双击连线:", cell.id);
        this.$emit("edge-double-click", cell);
      });

      // 监听选择事件
      this.graph.on("selection:changed", ({ added, removed }) => {
        this.$emit("selection-changed", { added, removed });
      });

      // 键盘快捷键
      this.graph.bindKey(["meta+c", "ctrl+c"], () => {
        const cells = this.graph.getSelectedCells();
        if (cells.length) {
          this.graph.copy(cells);
        }
        return false;
      });

      this.graph.bindKey(["meta+v", "ctrl+v"], () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 });
          this.graph.cleanSelection();
          this.graph.select(cells);
        }
        return false;
      });

      this.graph.bindKey(["delete", "backspace"], () => {
        const cells = this.graph.getSelectedCells();
        if (cells.length) {
          this.graph.removeCells(cells);
        }
        return false;
      });
    },

    // 处理拖拽悬停
    handleDragOver(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
    },

    // 处理拖拽放置
    handleDropEvent(e) {
      e.preventDefault();
      handleDrop(e, this.graph);
    },
  },
};
</script>

<style scoped>
.workflow-canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f5f5f5;
  overflow: hidden; /* 防止滚动条 */
}

.workflow-canvas {
  width: 100%;
  height: 100%;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  position: relative;
  box-sizing: border-box; /* 包含边框在内的盒模型 */
}

/* X6 相关样式 */
:deep(.x6-graph-scroller) {
  border: 1px solid #d9d9d9;
}

:deep(.x6-widget-selection-rubberband) {
  border: 1px solid #239edd;
  background-color: rgba(35, 158, 221, 0.1);
}

:deep(.x6-widget-selection-box) {
  opacity: 0;
}

:deep(.x6-widget-selection-box:hover) {
  opacity: 1;
}

/* 连接桩样式 - 改为始终可见 */
:deep(.x6-port-body) {
  visibility: visible;
  opacity: 0.6;
  transition: opacity 0.2s;
}

:deep(.x6-node:hover .x6-port-body) {
  opacity: 1;
}

/* 按钮工具样式 */
:deep(.x6-widget-tool-button) {
  opacity: 0.8;
  transition: opacity 0.2s;
}

:deep(.x6-widget-tool-button:hover) {
  opacity: 1;
}

/* 节点标签样式 */
:deep(.x6-node-label) {
  pointer-events: none;
  user-select: none;
}

/* 连线样式 */
:deep(.x6-edge) {
  cursor: pointer;
}

:deep(.x6-edge:hover path:nth-child(2)) {
  stroke: #1890ff;
  stroke-width: 2;
}

:deep(.x6-edge-selected path:nth-child(2)) {
  stroke: #1890ff;
  stroke-width: 2;
}
</style>
