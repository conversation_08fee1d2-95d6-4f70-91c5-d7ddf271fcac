<template>
  <div class="workflow-canvas">
    <div class="canvas-toolbar">
      <el-button-group>
        <el-button size="small" @click="zoomIn">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomOut">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomToFit">
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button size="small" @click="undo" :disabled="!canUndo">
          <el-icon><RefreshLeft /></el-icon>
        </el-button>
        <el-button size="small" @click="redo" :disabled="!canRedo">
          <el-icon><RefreshRight /></el-icon>
        </el-button>
      </el-button-group>

      <el-button size="small" @click="clearCanvas" type="danger">
        <el-icon><Delete /></el-icon>
        清空画布
      </el-button>

      <el-button size="small" @click="testAddNode" type="primary">
        测试添加节点
      </el-button>

      <el-button size="small" @click="testBasicNode" type="success">
        测试基础节点
      </el-button>

      <el-button size="small" @click="testCreateEdge" type="warning">
        测试创建连线
      </el-button>

      <el-button size="small" @click="refreshEdges" type="info">
        刷新连线显示
      </el-button>
    </div>

    <div ref="canvasContainer" class="canvas-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { Graph, Edge } from "@antv/x6";
import { Dnd } from "@antv/x6-plugin-dnd";
import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { History } from "@antv/x6-plugin-history";
import { Transform } from "@antv/x6-plugin-transform";
import {
  ZoomIn,
  ZoomOut,
  FullScreen,
  RefreshLeft,
  RefreshRight,
  Delete,
} from "@element-plus/icons-vue";

// 定义事件
const emit = defineEmits(["node-settings"]);

// 响应式数据
const canvasContainer = ref(null);
const graph = ref(null);
const dnd = ref(null);
const canUndo = ref(false);
const canRedo = ref(false);

// 注册自定义节点
const registerCustomNode = () => {
  Graph.registerNode(
    "workflow-node",
    {
      inherit: "rect",
      width: 180,
      height: 60,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: "#409eff",
          fill: "#ffffff",
          rx: 8,
          ry: 8,
        },
        text: {
          fontSize: 14,
          fill: "#303133",
          textAnchor: "middle",
          refX: "50%",
          refY: "50%",
          textVerticalAnchor: "middle",
        },
      },
      markup: [
        {
          tagName: "rect",
          selector: "body",
        },
        {
          tagName: "text",
          selector: "text",
        },
        {
          tagName: "circle",
          selector: "settings-btn",
          attrs: {
            r: 10,
            cx: 160,
            cy: 20,
            fill: "#f5f7fa",
            stroke: "#dcdfe6",
            strokeWidth: 1,
            cursor: "pointer",
          },
        },
        {
          tagName: "text",
          selector: "settings-icon",
          attrs: {
            x: 160,
            y: 20,
            fontSize: 12,
            fill: "#606266",
            textAnchor: "middle",
            dominantBaseline: "middle",
            cursor: "pointer",
            pointerEvents: "none",
          },
          textContent: "⚙",
        },
      ],
      ports: {
        groups: {
          top: {
            position: "top",
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: "#409eff",
                strokeWidth: 2,
                fill: "#fff",
                style: {
                  visibility: "visible",
                },
              },
            },
            markup: [
              {
                tagName: "circle",
                selector: "body",
              },
            ],
          },
          bottom: {
            position: "bottom",
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: "#409eff",
                strokeWidth: 2,
                fill: "#fff",
                style: {
                  visibility: "visible",
                },
              },
            },
            markup: [
              {
                tagName: "circle",
                selector: "body",
              },
            ],
          },
        },
        items: [
          {
            group: "top",
            id: "input",
          },
          {
            group: "bottom",
            id: "output",
          },
        ],
      },
    },
    true
  );

  console.log("自定义节点注册完成");
};

// 初始化画布
const initCanvas = () => {
  if (!canvasContainer.value) return;

  graph.value = new Graph({
    container: canvasContainer.value,
    width: canvasContainer.value.clientWidth,
    height: canvasContainer.value.clientHeight,
    background: {
      color: "#fafafa",
    },
    grid: {
      size: 20,
      visible: true,
      type: "dot",
      args: {
        color: "#e0e0e0",
        thickness: 1,
      },
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 3,
    },
    connecting: {
      router: {
        name: "manhattan",
        args: {
          padding: 1,
        },
      },
      connector: {
        name: "rounded",
        args: {
          radius: 8,
        },
      },
      anchor: "center",
      connectionPoint: "anchor",
      allowBlank: false,
      allowLoop: false,
      allowNode: false,
      allowEdge: false,
      allowPort: true,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Edge({
          shape: "edge",
          attrs: {
            line: {
              stroke: "#409eff",
              strokeWidth: 3,
              fill: "none",
              strokeDasharray: "0",
              targetMarker: {
                name: "block",
                width: 12,
                height: 8,
                fill: "#409eff",
                stroke: "#409eff",
              },
            },
          },
          zIndex: 1,
        });
      },
      validateConnection({
        sourceMagnet,
        targetMagnet,
        sourceView,
        targetView,
        sourcePort,
        targetPort,
      }) {
        console.log("验证连接:", {
          sourceMagnet,
          targetMagnet,
          sourceView,
          targetView,
          sourcePort,
          targetPort,
        });

        // 不能连接到自己
        if (sourceView === targetView) {
          console.log("不能连接到自己");
          return false;
        }

        // 必须有目标磁铁
        if (!targetMagnet) {
          console.log("没有目标磁铁");
          return false;
        }

        // 检查端口类型，输出端口只能连接到输入端口
        if (sourcePort && targetPort) {
          const sourceNode = sourceView.cell;
          const targetNode = targetView.cell;
          const sourcePortData = sourceNode.getPort(sourcePort);
          const targetPortData = targetNode.getPort(targetPort);

          console.log("源端口:", sourcePortData);
          console.log("目标端口:", targetPortData);

          // 输出端口(bottom)只能连接到输入端口(top)
          if (
            sourcePortData?.group === "bottom" &&
            targetPortData?.group === "top"
          ) {
            console.log("连接验证通过");
            return true;
          }

          console.log("端口类型不匹配");
          return false;
        }

        console.log("连接验证通过（默认）");
        return true;
      },
    },
  });

  // 注册插件
  graph.value
    .use(
      new Selection({
        enabled: true,
        multiple: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
      })
    )
    .use(new Snapline({ enabled: true }))
    .use(new Keyboard({ enabled: true }))
    .use(new Clipboard({ enabled: true }))
    .use(new History({ enabled: true }))
    .use(
      new Transform({
        resizing: true,
        rotating: false,
      })
    );

  // 初始化拖拽
  dnd.value = new Dnd({
    target: graph.value,
    scaled: false,
    dndContainer: canvasContainer.value,
  });

  // 绑定事件
  bindEvents();
};

// 绑定事件
const bindEvents = () => {
  if (!graph.value) return;

  // 监听历史变化
  graph.value.on("history:change", () => {
    canUndo.value = graph.value.canUndo();
    canRedo.value = graph.value.canRedo();
  });

  // 监听节点点击事件
  graph.value.on("node:click", ({ e, node }) => {
    const target = e.target;
    console.log("节点点击事件", target);
    console.log("target tagName:", target.tagName);
    console.log("target selector:", target.getAttribute("data-selector"));
    console.log("target textContent:", target.textContent);

    // 检查是否点击了设置按钮或设置图标
    const isSettingsClick =
      target.getAttribute("data-selector") === "settings-btn" ||
      target.getAttribute("data-selector") === "settings-icon" ||
      target.textContent === "⚙" ||
      (target.tagName === "circle" && target.getAttribute("r") === "10") ||
      (target.tagName === "text" && target.textContent === "⚙");

    if (isSettingsClick) {
      console.log("检测到设置按钮点击");

      const bbox = node.getBBox();
      const position = graph.value.localToGraph(
        bbox.x + bbox.width - 20,
        bbox.y + 20
      );
      const clientPosition = graph.value.localToClient(position);

      emit(
        "node-settings",
        {
          id: node.id,
          data: node.getData(),
          name: node.getAttrByPath("text/text") || "未命名节点",
        },
        {
          x: clientPosition.x,
          y: clientPosition.y,
        }
      );
    } else {
      console.log("普通节点点击，不是设置按钮");
    }
  });

  // 监听拖拽放置事件
  graph.value.on("blank:drop", (e) => {
    console.log("拖拽放置事件触发", e);
    const data = e.dataTransfer?.getData("application/json");
    if (data) {
      try {
        const modelData = JSON.parse(data);
        console.log("解析的模型数据:", modelData);
        const position = graph.value.clientToLocal(e.clientX, e.clientY);
        console.log("计算的位置:", position);
        addNode(modelData, position);
      } catch (error) {
        console.error("解析拖拽数据失败:", error);
      }
    }
  });

  // 监听画布的原生拖拽事件
  const canvasElement = canvasContainer.value;
  if (canvasElement) {
    console.log("设置画布拖拽事件监听器");

    canvasElement.addEventListener("dragenter", (e) => {
      e.preventDefault();
      console.log("dragenter事件");
    });

    canvasElement.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      console.log("dragover事件");
    });

    canvasElement.addEventListener("drop", (e) => {
      e.preventDefault();
      console.log("=== 原生drop事件触发 ===", e);
      console.log("dataTransfer:", e.dataTransfer);

      const data = e.dataTransfer?.getData("application/json");
      console.log("获取到的数据:", data);

      if (data) {
        try {
          const modelData = JSON.parse(data);
          console.log("解析的模型数据:", modelData);

          const rect = canvasElement.getBoundingClientRect();
          const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
          };
          console.log("计算的位置:", position);

          addNode(modelData, position);
        } catch (error) {
          console.error("解析数据失败:", error);
        }
      } else {
        console.warn("没有获取到拖拽数据");
      }
    });

    canvasElement.addEventListener("dragleave", () => {
      console.log("dragleave事件");
    });
  }

  // 监听连线相关事件
  graph.value.on("edge:connected", ({ edge }) => {
    console.log("连线创建成功:", edge);
    console.log("连线ID:", edge.id);
    console.log("连线源:", edge.getSource());
    console.log("连线目标:", edge.getTarget());
    console.log("连线属性:", edge.getAttrs());

    // 检查连线是否在画布上
    const edges = graph.value.getEdges();
    console.log("画布上的所有连线:", edges);

    // 强制设置连线样式，确保可见
    setTimeout(() => {
      edge.setAttrs({
        line: {
          stroke: "#409eff",
          strokeWidth: 3,
          strokeDasharray: "0",
          targetMarker: {
            name: "block",
            width: 12,
            height: 8,
            fill: "#409eff",
          },
        },
      });

      // 强制重新渲染
      edge.toFront();
      console.log("强制设置连线样式完成");
    }, 50);
  });

  graph.value.on("edge:added", ({ edge }) => {
    console.log("连线已添加到画布:", edge);
  });

  graph.value.on("edge:removed", ({ edge }) => {
    console.log("连线已删除:", edge);
  });

  graph.value.on("node:port:mouseenter", ({ port }) => {
    console.log("鼠标进入端口:", port);
  });

  graph.value.on("node:port:mouseleave", ({ port }) => {
    console.log("鼠标离开端口:", port);
  });

  // 监听连线开始事件
  graph.value.on("edge:mousedown", ({ edge }) => {
    console.log("开始连线:", edge);
  });

  // 监听连线过程中的事件
  graph.value.on("edge:connecting", ({ edge }) => {
    console.log("连线中:", edge);
  });

  // 监听连线移动事件
  graph.value.on("edge:mousemove", ({ edge }) => {
    console.log("连线移动:", edge);
  });

  // 监听连线验证事件
  graph.value.on("edge:validate", ({ edge, valid }) => {
    console.log("连线验证:", edge, "有效:", valid);
  });

  // 键盘快捷键
  graph.value.bindKey(["meta+z", "ctrl+z"], () => {
    if (graph.value.canUndo()) {
      graph.value.undo();
    }
    return false;
  });

  graph.value.bindKey(["meta+shift+z", "ctrl+y"], () => {
    if (graph.value.canRedo()) {
      graph.value.redo();
    }
    return false;
  });

  graph.value.bindKey(["meta+c", "ctrl+c"], () => {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.copy(cells);
    }
    return false;
  });

  graph.value.bindKey(["meta+v", "ctrl+v"], () => {
    if (!graph.value.isClipboardEmpty()) {
      const cells = graph.value.paste({ offset: 32 });
      graph.value.cleanSelection();
      graph.value.select(cells);
    }
    return false;
  });

  graph.value.bindKey("delete", () => {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.removeCells(cells);
    }
  });
};

// 添加节点
const addNode = (modelData, position) => {
  console.log("添加节点", modelData, position);

  if (!graph.value) {
    console.error("图形实例不存在");
    return null;
  }

  try {
    // 为从ModelList拖拽的节点添加端口配置
    const enhancedModelData = {
      ...modelData,
      nodeType: modelData.nodeType || "workflow-node",
      ports: modelData.ports || {
        input: { id: "input", group: "top", required: true },
        output: { id: "output", group: "bottom", required: true },
      },
    };

    console.log("增强后的模型数据:", enhancedModelData);

    // 使用自定义的workflow-node节点
    const node = graph.value.addNode({
      shape: "workflow-node",
      x: position.x - 90,
      y: position.y - 30,
      data: enhancedModelData,
      attrs: {
        text: {
          text: modelData.name || "未命名节点",
        },
      },
      // 显式添加端口配置
      ports: [
        {
          id: "input",
          group: "top",
        },
        {
          id: "output",
          group: "bottom",
        },
      ],
    });

    console.log("自定义节点添加成功", node);
    console.log("节点端口:", node.getPorts());

    // 确保端口被正确添加
    const ports = node.getPorts();
    if (ports.length === 0) {
      console.warn("端口未正确添加，手动添加端口");
      node.addPort({
        id: "input",
        group: "top",
      });
      node.addPort({
        id: "output",
        group: "bottom",
      });
    }

    // 强制刷新端口显示
    setTimeout(() => {
      const finalPorts = node.getPorts();
      console.log("最终端口列表:", finalPorts);

      finalPorts.forEach((port) => {
        // 确保端口可见
        node.setPortProp(port.id, "attrs/circle/style/visibility", "visible");
        node.setPortProp(port.id, "attrs/circle/magnet", true);
        node.setPortProp(port.id, "attrs/circle/stroke", "#409eff");
        node.setPortProp(port.id, "attrs/circle/strokeWidth", 2);
        node.setPortProp(port.id, "attrs/circle/fill", "#fff");
        node.setPortProp(port.id, "attrs/circle/r", 6);
      });

      console.log("端口样式设置完成");
    }, 100);

    // 添加连接桩悬停效果
    node.on("mouseenter", () => {
      const ports = node.getPorts();
      ports.forEach((port) => {
        node.setPortProp(port.id, "attrs/circle/fill", "#409eff");
        node.setPortProp(port.id, "attrs/circle/r", 8);
      });
    });

    node.on("mouseleave", () => {
      const ports = node.getPorts();
      ports.forEach((port) => {
        node.setPortProp(port.id, "attrs/circle/fill", "#fff");
        node.setPortProp(port.id, "attrs/circle/r", 6);
      });
    });

    return node;
  } catch (error) {
    console.error("添加自定义节点失败", error);

    // 如果失败，尝试基本的rect节点
    try {
      const rectNode = graph.value.addNode({
        shape: "rect",
        x: position.x - 90,
        y: position.y - 30,
        width: 180,
        height: 60,
        data: modelData,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#409eff",
            fill: "#ffffff",
            rx: 8,
            ry: 8,
          },
          text: {
            text: modelData.name || "未命名节点",
            fontSize: 14,
            fill: "#303133",
            textAnchor: "middle",
            refX: "50%",
            refY: "50%",
            textVerticalAnchor: "middle",
          },
        },
        // 为基本节点也添加端口
        ports: {
          groups: {
            top: {
              position: "top",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#409eff",
                  strokeWidth: 2,
                  fill: "#fff",
                },
              },
            },
            bottom: {
              position: "bottom",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#409eff",
                  strokeWidth: 2,
                  fill: "#fff",
                },
              },
            },
          },
          items: [
            { id: "input", group: "top" },
            { id: "output", group: "bottom" },
          ],
        },
      });
      console.log("基本节点添加成功", rectNode);
      return rectNode;
    } catch (simpleError) {
      console.error("基本节点添加也失败", simpleError);
      return null;
    }
  }
};

// 工具栏方法
const zoomIn = () => {
  graph.value?.zoom(0.1);
};

const zoomOut = () => {
  graph.value?.zoom(-0.1);
};

const zoomToFit = () => {
  graph.value?.zoomToFit({ padding: 20 });
};

const undo = () => {
  if (graph.value?.canUndo()) {
    graph.value.undo();
  }
};

const redo = () => {
  if (graph.value?.canRedo()) {
    graph.value.redo();
  }
};

const clearCanvas = () => {
  graph.value?.clearCells();
};

// 测试添加节点
const testAddNode = () => {
  const testModel1 = {
    id: "test-node-1",
    name: "测试节点1",
    description: "这是第一个测试节点",
  };

  const testModel2 = {
    id: "test-node-2",
    name: "测试节点2",
    description: "这是第二个测试节点",
  };

  // 添加两个节点用于测试手动连线
  const node1 = addNode(testModel1, { x: 200, y: 100 });
  const node2 = addNode(testModel2, { x: 400, y: 200 });

  // 延迟一下，确保节点创建完成后再检查端口
  setTimeout(() => {
    if (node1 && node2) {
      console.log("节点1端口:", node1.getPorts());
      console.log("节点2端口:", node2.getPorts());
      console.log(
        "请手动从节点1的下方连接桩拖拽到节点2的上方连接桩进行连线测试"
      );
    }
  }, 100);
};

// 测试基础节点连线
const testBasicNode = () => {
  if (!graph.value) return;

  // 创建两个基础的rect节点，带有端口
  const node1 = graph.value.addNode({
    shape: "rect",
    x: 100,
    y: 100,
    width: 120,
    height: 60,
    label: "基础节点1",
    attrs: {
      body: {
        stroke: "#409eff",
        strokeWidth: 2,
        fill: "#fff",
      },
    },
    ports: {
      groups: {
        top: {
          position: "top",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
        bottom: {
          position: "bottom",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
      },
      items: [
        { id: "input", group: "top" },
        { id: "output", group: "bottom" },
      ],
    },
  });

  const node2 = graph.value.addNode({
    shape: "rect",
    x: 300,
    y: 200,
    width: 120,
    height: 60,
    label: "基础节点2",
    attrs: {
      body: {
        stroke: "#409eff",
        strokeWidth: 2,
        fill: "#fff",
      },
    },
    ports: {
      groups: {
        top: {
          position: "top",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
        bottom: {
          position: "bottom",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
      },
      items: [
        { id: "input", group: "top" },
        { id: "output", group: "bottom" },
      ],
    },
  });

  console.log("基础节点1端口:", node1.getPorts());
  console.log("基础节点2端口:", node2.getPorts());
  console.log("请尝试从基础节点1的下方连接桩拖拽到基础节点2的上方连接桩");
};

// 测试创建连线
const testCreateEdge = () => {
  if (!graph.value) return;

  // 先创建两个简单的节点
  const node1 = graph.value.addNode({
    shape: "rect",
    x: 50,
    y: 50,
    width: 100,
    height: 40,
    label: "节点A",
    attrs: {
      body: {
        stroke: "#409eff",
        strokeWidth: 2,
        fill: "#fff",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
      },
      items: [{ id: "out", group: "out" }],
    },
  });

  const node2 = graph.value.addNode({
    shape: "rect",
    x: 250,
    y: 50,
    width: 100,
    height: 40,
    label: "节点B",
    attrs: {
      body: {
        stroke: "#409eff",
        strokeWidth: 2,
        fill: "#fff",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              stroke: "#409eff",
              strokeWidth: 2,
              fill: "#fff",
            },
          },
        },
      },
      items: [{ id: "in", group: "in" }],
    },
  });

  // 手动创建连线
  setTimeout(() => {
    console.log("开始创建测试连线");

    try {
      const edge = graph.value.addEdge({
        source: { cell: node1.id, port: "out" },
        target: { cell: node2.id, port: "in" },
        attrs: {
          line: {
            stroke: "#409eff",
            strokeWidth: 3,
            targetMarker: {
              name: "block",
              width: 12,
              height: 8,
              fill: "#409eff",
            },
          },
        },
      });

      console.log("手动连线创建成功:", edge);
      console.log("连线是否在画布上:", graph.value.hasCell(edge));
    } catch (error) {
      console.error("手动创建连线失败:", error);
    }
  }, 100);
};

// 刷新连线显示
const refreshEdges = () => {
  if (!graph.value) return;

  const edges = graph.value.getEdges();
  console.log("当前画布上的连线数量:", edges.length);

  edges.forEach((edge, index) => {
    console.log(`连线 ${index + 1}:`, {
      id: edge.id,
      source: edge.getSource(),
      target: edge.getTarget(),
      attrs: edge.getAttrs(),
    });

    // 强制设置连线样式
    edge.setAttrs({
      line: {
        stroke: "#ff4d4f", // 临时使用红色，便于观察
        strokeWidth: 4,
        fill: "none",
        strokeDasharray: "0",
        targetMarker: {
          name: "block",
          width: 12,
          height: 8,
          fill: "#ff4d4f",
          stroke: "#ff4d4f",
        },
      },
    });

    // 强制重新渲染
    edge.toFront();
  });

  // 2秒后恢复正常颜色
  setTimeout(() => {
    edges.forEach((edge) => {
      edge.setAttrs({
        line: {
          stroke: "#409eff",
          strokeWidth: 3,
          fill: "none",
          strokeDasharray: "0",
          targetMarker: {
            name: "block",
            width: 12,
            height: 8,
            fill: "#409eff",
            stroke: "#409eff",
          },
        },
      });
    });
    console.log("连线颜色已恢复正常");
  }, 2000);
};

// 暴露给父组件的方法
const deleteNode = (nodeId) => {
  const node = graph.value?.getCellById(nodeId);
  if (node) {
    graph.value.removeCell(node);
  }
};

const copyNode = (nodeId) => {
  const node = graph.value?.getCellById(nodeId);
  if (node) {
    graph.value.copy([node]);
    const cells = graph.value.paste({ offset: 32 });
    graph.value.cleanSelection();
    graph.value.select(cells);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (graph.value && canvasContainer.value) {
    graph.value.resize(
      canvasContainer.value.clientWidth,
      canvasContainer.value.clientHeight
    );
  }
};

// 暴露方法给父组件
defineExpose({
  deleteNode,
  copyNode,
  addNode,
  graph: () => graph.value,
});

onMounted(async () => {
  await nextTick();
  registerCustomNode();
  initCanvas();

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (graph.value) {
    graph.value.dispose();
  }
});
</script>

<style lang="scss" scoped>
.workflow-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fafafa;
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

// 全局样式，用于改善X6节点的显示
:deep(.x6-node) {
  .x6-port {
    visibility: visible !important; // 连接桩始终可见
    transition: all 0.2s ease;
  }

  .x6-port-body {
    fill: #fff;
    stroke: #409eff;
    stroke-width: 2;
    r: 6;
    transition: all 0.2s ease;
    cursor: crosshair; // 鼠标样式提示可连接
  }

  .x6-port-body:hover {
    fill: #409eff;
    stroke: #409eff;
    r: 8;
    stroke-width: 3;
  }
}

// 连线时高亮所有端口
:deep(.x6-graph-connecting) {
  .x6-node .x6-port-body {
    fill: #e6f7ff !important;
    stroke: #409eff !important;
    stroke-width: 3 !important;
    r: 8 !important;
  }
}

// 可连接的端口高亮
:deep(.x6-port-magnet-available) {
  fill: #52c41a !important;
  stroke: #52c41a !important;
}

// 连线样式
:deep(.x6-edge) {
  .x6-edge-path {
    stroke: #409eff;
    stroke-width: 2;
    fill: none;
  }

  .x6-edge-marker {
    fill: #409eff;
    stroke: #409eff;
  }
}

// 确保连线在正确的层级
:deep(.x6-graph-svg) {
  .x6-graph-svg-stage {
    .x6-graph-svg-viewport {
      .x6-edge {
        pointer-events: all;
        z-index: 1;
      }
    }
  }
}

// 设置按钮样式
:deep(.workflow-node) {
  .settings-btn {
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      fill: #409eff;
      stroke: #409eff;
    }
  }

  .settings-icon {
    pointer-events: none;
    user-select: none;
  }
}
</style>
