<template>
  <div class="workflow-canvas">
    <div class="canvas-toolbar">
      <el-button-group>
        <el-button size="small" @click="zoomIn">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomOut">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomToFit">
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button size="small" @click="undo" :disabled="!canUndo">
          <el-icon><RefreshLeft /></el-icon>
        </el-button>
        <el-button size="small" @click="redo" :disabled="!canRedo">
          <el-icon><RefreshRight /></el-icon>
        </el-button>
      </el-button-group>
      <el-button size="small" @click="clearCanvas" type="danger">
        <el-icon><Delete /></el-icon>
        清空画布
      </el-button>
    </div>

    <div ref="canvasContainer" class="canvas-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { Graph } from "@antv/x6";
import { Dnd } from "@antv/x6-plugin-dnd";
import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { History } from "@antv/x6-plugin-history";
import { Transform } from "@antv/x6-plugin-transform";
import {
  ZoomIn,
  ZoomOut,
  FullScreen,
  RefreshLeft,
  RefreshRight,
  Delete,
} from "@element-plus/icons-vue";

// 定义事件
const emit = defineEmits(["node-settings"]);

// 响应式数据
const canvasContainer = ref(null);
const graph = ref(null);
const dnd = ref(null);
const canUndo = ref(false);
const canRedo = ref(false);

// 注册自定义节点
const registerCustomNode = () => {
  Graph.registerNode(
    "workflow-node",
    {
      inherit: "rect",
      width: 180,
      height: 40,
      attrs: {
        inherit: "rect",
        width: 66,
        height: 36,
        body: {
          strokeWidth: 2,
          stroke: "#409eff",
          fill: "#ffffff",
          rx: 4,
          ry: 4,
        },
        text: {
          fontSize: 14,
          fill: "#303133",
          textAnchor: "middle",
          refX: "25%",
          refY: "50%",
          textVerticalAnchor: "middle",
        },
      },
      markup: [
        {
          tagName: "rect",
          selector: "body",
        },
        {
          tagName: "text",
          selector: "text",
        },
        {
          tagName: "circle",
          selector: "settings-btn",
          attrs: {
            r: 10,
            cx: 160,
            cy: 20,
            fill: "#f5f7fa",
            stroke: "#dcdfe6",
            strokeWidth: 1,
            cursor: "pointer",
          },
        },
        {
          tagName: "text",
          selector: "settings-icon",
          attrs: {
            x: 160,
            y: 20,
            fontSize: 16,
            fill: "#606266",
            textAnchor: "middle",
            dominantBaseline: "middle",
            cursor: "pointer",
            pointerEvents: "none",
          },
          textContent: "⚙",
        },
      ],
      ports: {
        groups: {
          top: {
            position: "top",
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: "#409eff",
                strokeWidth: 2,
                fill: "#fff",
                style: {
                  visibility: "visible",
                },
              },
            },
            markup: [
              {
                tagName: "circle",
                selector: "body",
              },
            ],
          },
          bottom: {
            position: "bottom",
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: "#409eff",
                strokeWidth: 2,
                fill: "#fff",
                style: {
                  visibility: "visible",
                },
              },
            },
            markup: [
              {
                tagName: "circle",
                selector: "body",
              },
            ],
          },
        },
        items: [
          {
            group: "top",
            id: "input",
          },
          {
            group: "bottom",
            id: "output",
          },
        ],
      },
    },
    true
  );
};

// 初始化画布
const initCanvas = () => {
  if (!canvasContainer.value) return;

  graph.value = new Graph({
    container: canvasContainer.value,
    width: canvasContainer.value.clientWidth,
    height: canvasContainer.value.clientHeight,
    grid: true,
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 3,
    },
    connecting: {
      router: "manhattan",
      connector: {
        name: "rounded",
        args: {
          radius: 8,
        },
      },
      anchor: "center",
      connectionPoint: "anchor",
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        console.log("🔗 创建新连线");
        return this.createEdge({
          attrs: {
            line: {
              stroke: "#A2B1C3",
              strokeWidth: 2,
              targetMarker: {
                name: "block",
                width: 12,
                height: 8,
              },
            },
          },
        });
      },
      validateConnection({
        sourceMagnet,
        targetMagnet,
        sourceView,
        targetView,
        sourcePort,
        targetPort,
      }) {
        console.log("=== 连接验证开始 ===");
        console.log("验证连接:", {
          sourceMagnet,
          targetMagnet,
          sourceView: sourceView?.cell?.id,
          targetView: targetView?.cell?.id,
          sourcePort,
          targetPort,
        });

        // 不能连接到自己
        if (sourceView === targetView) {
          console.log("❌ 验证失败: 不能连接到自己");
          return false;
        }

        // 必须有目标磁铁
        if (!targetMagnet) {
          console.log("❌ 验证失败: 没有目标磁铁");
          return false;
        }

        // 检查端口类型，输出端口只能连接到输入端口
        if (sourcePort && targetPort) {
          const sourceNode = sourceView.cell;
          const targetNode = targetView.cell;
          const sourcePortData = sourceNode.getPort(sourcePort);
          const targetPortData = targetNode.getPort(targetPort);

          console.log("源端口数据:", sourcePortData);
          console.log("目标端口数据:", targetPortData);

          // 输出端口(bottom)只能连接到输入端口(top)
          if (
            sourcePortData?.group === "bottom" &&
            targetPortData?.group === "top"
          ) {
            console.log("✅ 验证通过: 输出端口连接到输入端口");
            return true;
          }

          // 也允许top到bottom的连接，增加灵活性
          if (
            sourcePortData?.group === "top" &&
            targetPortData?.group === "bottom"
          ) {
            console.log("✅ 验证通过: 输入端口连接到输出端口");
            return true;
          }

          console.log("❌ 验证失败: 端口类型不匹配", {
            sourceGroup: sourcePortData?.group,
            targetGroup: targetPortData?.group,
          });
          return false;
        }

        console.log("✅ 验证通过: 默认允许连接");
        return true;
      },
    },
  });

  // 注册插件
  graph.value
    .use(
      new Selection({
        enabled: true,
        multiple: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
      })
    )
    .use(new Snapline({ enabled: true }))
    .use(new Keyboard({ enabled: true }))
    .use(new Clipboard({ enabled: true }))
    .use(new History({ enabled: true }))
    .use(
      new Transform({
        resizing: false,
        rotating: false,
      })
    );

  // 初始化拖拽
  dnd.value = new Dnd({
    target: graph.value,
    scaled: false,
    dndContainer: canvasContainer.value,
  });

  // 绑定事件
  bindEvents();
};

// 绑定事件
const bindEvents = () => {
  if (!graph.value) return;

  // 监听历史变化
  graph.value.on("history:change", () => {
    canUndo.value = graph.value.canUndo();
    canRedo.value = graph.value.canRedo();
  });

  // 监听节点点击事件
  graph.value.on("node:click", ({ e, node }) => {
    const target = e.target;
    console.log("节点点击事件", target);
    console.log("target tagName:", target.tagName);
    console.log("target selector:", target.getAttribute("data-selector"));
    console.log("target textContent:", target.textContent);

    // 检查是否点击了设置按钮或设置图标
    const isSettingsClick =
      target.getAttribute("data-selector") === "settings-btn" ||
      target.getAttribute("data-selector") === "settings-icon" ||
      target.textContent === "⚙" ||
      (target.tagName === "circle" && target.getAttribute("r") === "10") ||
      (target.tagName === "text" && target.textContent === "⚙");

    if (isSettingsClick) {
      console.log("检测到设置按钮点击");

      const bbox = node.getBBox();
      const position = graph.value.localToGraph(
        bbox.x + bbox.width - 20,
        bbox.y + 20
      );
      const clientPosition = graph.value.localToClient(position);

      emit(
        "node-settings",
        {
          id: node.id,
          data: node.getData(),
          name: node.getAttrByPath("text/text") || "未命名节点",
        },
        {
          x: clientPosition.x,
          y: clientPosition.y,
        }
      );
    } else {
      console.log("普通节点点击，不是设置按钮");
    }
  });

  // 监听拖拽放置事件
  graph.value.on("blank:drop", (e) => {
    console.log("拖拽放置事件触发", e);
    const data = e.dataTransfer?.getData("application/json");
    if (data) {
      try {
        const modelData = JSON.parse(data);
        console.log("解析的模型数据:", modelData);
        const position = graph.value.clientToLocal(e.clientX, e.clientY);
        console.log("计算的位置:", position);
        addNode(modelData, position);
      } catch (error) {
        console.error("解析拖拽数据失败:", error);
      }
    }
  });

  // 监听画布的原生拖拽事件
  const canvasElement = canvasContainer.value;
  if (canvasElement) {
    console.log("设置画布拖拽事件监听器");

    canvasElement.addEventListener("dragenter", (e) => {
      e.preventDefault();
      console.log("dragenter事件");
    });

    canvasElement.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      console.log("dragover事件");
    });

    canvasElement.addEventListener("drop", (e) => {
      e.preventDefault();
      console.log("=== 原生drop事件触发 ===", e);
      console.log("dataTransfer:", e.dataTransfer);

      const data = e.dataTransfer?.getData("application/json");
      console.log("获取到的数据:", data);

      if (data) {
        try {
          const modelData = JSON.parse(data);
          console.log("解析的模型数据:", modelData);

          const rect = canvasElement.getBoundingClientRect();
          const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
          };
          console.log("计算的位置:", position);

          addNode(modelData, position);
        } catch (error) {
          console.error("解析数据失败:", error);
        }
      } else {
        console.warn("没有获取到拖拽数据");
      }
    });

    canvasElement.addEventListener("dragleave", () => {
      console.log("dragleave事件");
    });
  }
  // 监听连线相关事件
  graph.value.on("edge:connected", ({ edge }) => {
    // 强制更新视图
    const view = graph.value.findViewByCell(edge);
    if (view) {
      view.update();
    }

    console.log("连线样式设置完成");
  });

  graph.value.on("edge:added", ({ edge }) => {
    console.log("连线已添加到画布:", edge);
  });

  graph.value.on("edge:removed", ({ edge }) => {
    console.log("连线已删除:", edge);
  });

  graph.value.on("node:port:mouseenter", ({ port }) => {
    console.log("鼠标进入端口:", port);
  });

  graph.value.on("node:port:mouseleave", ({ port }) => {
    console.log("鼠标离开端口:", port);
  });

  // 监听连线开始事件
  graph.value.on("edge:mousedown", ({ edge }) => {
    console.log("开始连线:", edge);
  });

  // 监听连线过程中的事件
  graph.value.on("edge:connecting", ({ edge }) => {
    console.log("连线中:", edge);
  });

  // 监听连线移动事件
  graph.value.on("edge:mousemove", ({ edge }) => {
    console.log("连线移动:", edge);
  });

  // 监听连线验证事件
  graph.value.on("edge:validate", ({ edge, valid }) => {
    console.log("连线验证:", edge, "有效:", valid);
  });

  // 键盘快捷键
  graph.value.bindKey(["meta+z", "ctrl+z"], () => {
    if (graph.value.canUndo()) {
      graph.value.undo();
    }
    return false;
  });

  graph.value.bindKey(["meta+shift+z", "ctrl+y"], () => {
    if (graph.value.canRedo()) {
      graph.value.redo();
    }
    return false;
  });

  graph.value.bindKey(["meta+c", "ctrl+c"], () => {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.copy(cells);
    }
    return false;
  });

  graph.value.bindKey(["meta+v", "ctrl+v"], () => {
    if (!graph.value.isClipboardEmpty()) {
      const cells = graph.value.paste({ offset: 32 });
      graph.value.cleanSelection();
      graph.value.select(cells);
    }
    return false;
  });

  graph.value.bindKey("delete", () => {
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.removeCells(cells);
    }
  });
};

// 添加节点
const addNode = (modelData, position) => {
  console.log("添加节点", modelData, position);

  if (!graph.value) {
    console.error("图形实例不存在");
    return null;
  }

  try {
    // 使用自定义的workflow-node节点，依赖registerCustomNode中的端口配置
    const node = graph.value.addNode({
      shape: "workflow-node",
      x: position.x - 90,
      y: position.y - 30,
      data: modelData,
      attrs: {
        text: {
          text: modelData.name || "未命名节点",
        },
      },
    });

    console.log("自定义节点添加成功", node);

    // 检查端口是否正确创建
    let ports = node.getPorts();
    console.log("初始端口:", ports);

    // 如果端口不存在，手动添加（这应该不会发生，但作为后备）
    if (ports.length === 0) {
      console.warn("端口未自动创建，手动添加端口");
      node.addPort({
        id: "input",
        group: "top",
      });
      node.addPort({
        id: "output",
        group: "bottom",
      });
      ports = node.getPorts();
    }

    // 确保端口属性正确设置（同步执行，避免时序问题）
    ports.forEach((port) => {
      console.log(`设置端口 ${port.id} 的属性`);
      // 确保端口可见且可连接
      node.setPortProp(port.id, "attrs/circle/magnet", true);
      node.setPortProp(port.id, "attrs/circle/stroke", "#409eff");
      node.setPortProp(port.id, "attrs/circle/strokeWidth", 2);
      node.setPortProp(port.id, "attrs/circle/fill", "#fff");
      node.setPortProp(port.id, "attrs/circle/r", 6);
      node.setPortProp(port.id, "attrs/circle/style/visibility", "visible");
    });

    console.log("端口属性设置完成，最终端口:", node.getPorts());

    // 添加连接桩悬停效果
    node.on("mouseenter", () => {
      const currentPorts = node.getPorts();
      currentPorts.forEach((port) => {
        node.setPortProp(port.id, "attrs/circle/fill", "#409eff");
        node.setPortProp(port.id, "attrs/circle/r", 8);
      });
    });

    node.on("mouseleave", () => {
      const currentPorts = node.getPorts();
      currentPorts.forEach((port) => {
        node.setPortProp(port.id, "attrs/circle/fill", "#fff");
        node.setPortProp(port.id, "attrs/circle/r", 6);
      });
    });

    return node;
  } catch (error) {
    console.error("添加自定义节点失败", error);

    // 如果失败，尝试基本的rect节点
    try {
      const rectNode = graph.value.addNode({
        shape: "rect",
        x: position.x - 90,
        y: position.y - 30,
        width: 180,
        height: 60,
        data: modelData,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#409eff",
            fill: "#ffffff",
            rx: 8,
            ry: 8,
          },
          text: {
            text: modelData.name || "未命名节点",
            fontSize: 14,
            fill: "#303133",
            textAnchor: "middle",
            refX: "50%",
            refY: "50%",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: "top",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#409eff",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "visible",
                  },
                },
              },
              markup: [
                {
                  tagName: "circle",
                  selector: "body",
                },
              ],
            },
            bottom: {
              position: "bottom",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#409eff",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "visible",
                  },
                },
              },
              markup: [
                {
                  tagName: "circle",
                  selector: "body",
                },
              ],
            },
          },
          items: [
            { id: "input", group: "top" },
            { id: "output", group: "bottom" },
          ],
        },
      });

      console.log("基本节点添加成功", rectNode);
      console.log("基本节点端口:", rectNode.getPorts());

      // 为基本节点也添加悬停效果
      rectNode.on("mouseenter", () => {
        const ports = rectNode.getPorts();
        ports.forEach((port) => {
          rectNode.setPortProp(port.id, "attrs/circle/fill", "#409eff");
          rectNode.setPortProp(port.id, "attrs/circle/r", 8);
        });
      });

      rectNode.on("mouseleave", () => {
        const ports = rectNode.getPorts();
        ports.forEach((port) => {
          rectNode.setPortProp(port.id, "attrs/circle/fill", "#fff");
          rectNode.setPortProp(port.id, "attrs/circle/r", 6);
        });
      });

      return rectNode;
    } catch (simpleError) {
      console.error("基本节点添加也失败", simpleError);
      return null;
    }
  }
};

// 工具栏方法
const zoomIn = () => {
  graph.value?.zoom(0.1);
};

const zoomOut = () => {
  graph.value?.zoom(-0.1);
};

const zoomToFit = () => {
  graph.value?.zoomToFit({ padding: 20 });
};

const undo = () => {
  if (graph.value?.canUndo()) {
    graph.value.undo();
  }
};

const redo = () => {
  if (graph.value?.canRedo()) {
    graph.value.redo();
  }
};

const clearCanvas = () => {
  graph.value?.clearCells();
};

// 暴露给父组件的方法
const deleteNode = (nodeId) => {
  const node = graph.value?.getCellById(nodeId);
  if (node) {
    graph.value.removeCell(node);
  }
};

const copyNode = (nodeId) => {
  const node = graph.value?.getCellById(nodeId);
  if (node) {
    graph.value.copy([node]);
    const cells = graph.value.paste({ offset: 32 });
    graph.value.cleanSelection();
    graph.value.select(cells);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (graph.value && canvasContainer.value) {
    graph.value.resize(
      canvasContainer.value.clientWidth,
      canvasContainer.value.clientHeight
    );
  }
};

// 暴露方法给父组件
defineExpose({
  deleteNode,
  copyNode,
  addNode,
  graph: () => graph.value,
});

onMounted(async () => {
  await nextTick();
  registerCustomNode();
  initCanvas();

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (graph.value) {
    graph.value.dispose();
  }
});
</script>

<style lang="scss" scoped>
.workflow-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fafafa;
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}
</style>
